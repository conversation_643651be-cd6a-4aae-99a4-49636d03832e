// Test data generator for integration testing

export interface TestBlogPost {
  title: string
  content: string
  excerpt: string
  categories: string[]
  tags: string[]
  publishDate: string
  featured: boolean
}

export interface TestProject {
  title: string
  client: string
  industry: string
  technologyStack: string[]
  challenge: string
  solution: string
  content: string
  projectDate: string
  liveUrl?: string
  sourceUrl?: string
}

export interface TestComment {
  content: string
  authorName: string
  authorEmail: string
  postId: string
}

// Sample blog posts for testing
export const generateTestBlogPosts = (): TestBlogPost[] => [
  {
    title: "Integration Testing Best Practices for Modern Web Applications",
    content: `# Integration Testing Best Practices

Integration testing is a crucial phase in software development that ensures different components of your application work together seamlessly.

## Why Integration Testing Matters

When building modern web applications, individual components might work perfectly in isolation but fail when combined. Integration testing helps identify these issues early.

### Key Benefits

1. **Early Bug Detection**: Find issues before they reach production
2. **Improved Reliability**: Ensure components work together
3. **Better User Experience**: Validate complete user workflows
4. **Reduced Risk**: Minimize production failures

## Testing Strategies

### End-to-End Testing
Test complete user workflows from start to finish.

### API Integration Testing
Verify that your frontend correctly communicates with backend services.

### Database Integration
Ensure data flows correctly between your application and database.

## Best Practices

- **Test Real Scenarios**: Use realistic test data and user flows
- **Automate Where Possible**: Reduce manual testing overhead
- **Test Error Conditions**: Verify graceful error handling
- **Monitor Performance**: Ensure integrations don't impact performance

## Conclusion

Integration testing is essential for building reliable, user-friendly applications. Invest time in creating comprehensive integration tests to catch issues early and ensure a smooth user experience.`,
    excerpt: "Learn essential integration testing practices for modern web applications, including strategies for end-to-end testing, API integration, and performance monitoring.",
    categories: ["Testing", "Web Development"],
    tags: ["integration-testing", "best-practices", "web-development", "quality-assurance"],
    publishDate: new Date().toISOString(),
    featured: true
  },
  {
    title: "Building Scalable React Applications with Next.js",
    content: `# Building Scalable React Applications with Next.js

Next.js has revolutionized how we build React applications by providing a powerful framework with built-in optimizations.

## Key Features

### Server-Side Rendering (SSR)
Improve SEO and initial page load times with server-side rendering.

### Static Site Generation (SSG)
Generate static pages at build time for maximum performance.

### API Routes
Build full-stack applications with built-in API routes.

### Image Optimization
Automatic image optimization for better performance.

## Architecture Patterns

### Component Organization
Structure your components for maintainability and reusability.

### State Management
Choose the right state management solution for your application scale.

### Data Fetching
Implement efficient data fetching strategies with SWR or React Query.

## Performance Optimization

- **Code Splitting**: Automatically split code for optimal loading
- **Image Optimization**: Use Next.js Image component
- **Bundle Analysis**: Monitor and optimize bundle size
- **Caching Strategies**: Implement effective caching

## Deployment

Deploy your Next.js application to Vercel, Netlify, or other platforms with ease.

## Conclusion

Next.js provides an excellent foundation for building scalable React applications with built-in optimizations and best practices.`,
    excerpt: "Discover how to build scalable React applications using Next.js, covering SSR, SSG, performance optimization, and deployment strategies.",
    categories: ["React", "Next.js"],
    tags: ["react", "nextjs", "scalability", "performance", "ssr", "ssg"],
    publishDate: new Date(Date.now() - 86400000).toISOString(), // Yesterday
    featured: false
  },
  {
    title: "The Future of Web Development: Trends to Watch in 2024",
    content: `# The Future of Web Development: Trends to Watch in 2024

The web development landscape continues to evolve rapidly. Here are the key trends shaping the future of web development.

## Emerging Technologies

### WebAssembly (WASM)
Bringing near-native performance to web applications.

### Progressive Web Apps (PWAs)
Bridging the gap between web and native applications.

### Edge Computing
Moving computation closer to users for better performance.

## Development Trends

### Jamstack Architecture
Static site generators and headless CMS solutions.

### Micro-frontends
Breaking down monolithic frontends into manageable pieces.

### Serverless Functions
Event-driven, scalable backend solutions.

## AI Integration

### AI-Powered Development Tools
Code completion, bug detection, and automated testing.

### Intelligent User Interfaces
Personalized experiences powered by machine learning.

### Content Generation
AI-assisted content creation and optimization.

## Security Focus

### Zero Trust Architecture
Never trust, always verify approach to security.

### Privacy-First Design
Building applications with privacy as a core principle.

### Secure by Default
Security considerations from the ground up.

## Conclusion

The future of web development is exciting, with new technologies and approaches emerging constantly. Stay curious and keep learning!`,
    excerpt: "Explore the latest trends shaping web development in 2024, from WebAssembly and PWAs to AI integration and security-first approaches.",
    categories: ["Trends", "Future Tech"],
    tags: ["web-development", "trends", "2024", "webassembly", "pwa", "ai", "security"],
    publishDate: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
    featured: true
  }
]

// Sample projects for testing
export const generateTestProjects = (): TestProject[] => [
  {
    title: "E-commerce Platform Redesign",
    client: "TechCorp Solutions",
    industry: "E-commerce",
    technologyStack: ["React", "Next.js", "TypeScript", "Tailwind CSS", "Stripe", "Firebase"],
    challenge: "The client's existing e-commerce platform had poor user experience, slow loading times, and low conversion rates. The outdated design and complex checkout process were driving customers away.",
    solution: "We redesigned the entire platform with a focus on user experience, implementing a modern, responsive design with streamlined navigation and a simplified checkout process. We also optimized performance and integrated advanced analytics.",
    content: `## Project Overview

This comprehensive e-commerce platform redesign transformed a struggling online store into a high-converting, user-friendly shopping experience.

### Key Improvements

1. **Modern Design System**: Implemented a cohesive design system with consistent branding
2. **Performance Optimization**: Reduced page load times by 60%
3. **Mobile-First Approach**: Fully responsive design optimized for mobile shopping
4. **Streamlined Checkout**: Reduced checkout abandonment by 40%

### Technical Implementation

- **Frontend**: Built with React and Next.js for optimal performance
- **Styling**: Tailwind CSS for rapid, consistent styling
- **Payments**: Integrated Stripe for secure payment processing
- **Backend**: Firebase for real-time data and authentication
- **Analytics**: Comprehensive tracking and conversion optimization

### Results

- 150% increase in conversion rate
- 60% improvement in page load speed
- 40% reduction in checkout abandonment
- 200% increase in mobile sales

The project was completed on time and within budget, exceeding all performance targets.`,
    projectDate: "2024-01-15",
    liveUrl: "https://example-ecommerce.com",
    sourceUrl: "https://github.com/example/ecommerce-redesign"
  },
  {
    title: "AI-Powered Content Management System",
    client: "ContentFlow Media",
    industry: "Media & Publishing",
    technologyStack: ["Next.js", "TypeScript", "OpenAI API", "Supabase", "Vercel", "Tailwind CSS"],
    challenge: "ContentFlow Media needed a modern CMS that could help their writers create high-quality content more efficiently. Their existing system was outdated and lacked AI-powered features to assist with content creation.",
    solution: "We developed a cutting-edge CMS with integrated AI capabilities for content generation, editing assistance, and SEO optimization. The system includes real-time collaboration features and advanced analytics.",
    content: `## AI-Powered CMS Solution

A revolutionary content management system that leverages artificial intelligence to enhance the content creation process.

### Core Features

1. **AI Content Generation**: Generate blog posts, articles, and social media content
2. **Smart Editing**: AI-powered grammar checking and style suggestions
3. **SEO Optimization**: Automated SEO recommendations and meta tag generation
4. **Real-time Collaboration**: Multiple writers can collaborate simultaneously

### AI Integration

- **Content Research**: Automated research and fact-checking
- **Outline Generation**: AI-generated content outlines and structures
- **Image Suggestions**: AI-powered image recommendations
- **Performance Analytics**: AI-driven content performance insights

### Technical Architecture

- **Frontend**: Next.js with TypeScript for type safety
- **AI Services**: OpenAI GPT-4 integration for content generation
- **Database**: Supabase for real-time data synchronization
- **Deployment**: Vercel for seamless deployment and scaling
- **UI/UX**: Tailwind CSS for modern, responsive design

### Impact

- 300% increase in content production speed
- 85% improvement in SEO rankings
- 50% reduction in editing time
- 95% user satisfaction rate

The CMS has transformed how ContentFlow Media creates and manages content, making their team significantly more productive.`,
    projectDate: "2024-02-20",
    liveUrl: "https://contentflow-cms.com",
    sourceUrl: "https://github.com/example/ai-cms"
  },
  {
    title: "Real-time Collaboration Dashboard",
    client: "TeamSync Pro",
    industry: "SaaS",
    technologyStack: ["React", "Socket.io", "Node.js", "MongoDB", "Redis", "Docker"],
    challenge: "TeamSync Pro needed a real-time collaboration platform that could handle thousands of concurrent users while maintaining low latency and high reliability. Their existing solution couldn't scale to meet growing demand.",
    solution: "We built a highly scalable real-time dashboard using WebSocket technology, microservices architecture, and advanced caching strategies. The solution supports real-time collaboration, live updates, and seamless user experience.",
    content: `## Real-time Collaboration Platform

A high-performance dashboard enabling seamless real-time collaboration for distributed teams.

### Key Capabilities

1. **Real-time Updates**: Instant synchronization across all connected clients
2. **Live Collaboration**: Multiple users editing simultaneously
3. **Scalable Architecture**: Handles thousands of concurrent users
4. **Offline Support**: Seamless offline/online synchronization

### Technical Highlights

- **WebSocket Integration**: Socket.io for real-time communication
- **Microservices**: Scalable backend architecture
- **Caching Layer**: Redis for high-performance data access
- **Database**: MongoDB for flexible document storage
- **Containerization**: Docker for consistent deployment

### Performance Metrics

- **Latency**: < 50ms for real-time updates
- **Scalability**: Supports 10,000+ concurrent users
- **Uptime**: 99.9% availability
- **Response Time**: < 200ms for all operations

### User Experience

- Intuitive interface with drag-and-drop functionality
- Real-time cursors and user presence indicators
- Conflict resolution for simultaneous edits
- Comprehensive activity tracking and history

The platform has become the go-to solution for remote teams requiring seamless collaboration capabilities.`,
    projectDate: "2024-03-10",
    liveUrl: "https://teamsync-dashboard.com",
    sourceUrl: "https://github.com/example/realtime-dashboard"
  }
]

// Sample comments for testing
export const generateTestComments = (): TestComment[] => [
  {
    content: "Great article! The integration testing strategies you mentioned are exactly what we needed for our project. Thanks for sharing these insights.",
    authorName: "Sarah Johnson",
    authorEmail: "<EMAIL>",
    postId: "test-post-1"
  },
  {
    content: "I've been using Next.js for a while, but your explanation of SSG vs SSR really clarified some concepts for me. Looking forward to implementing these patterns in my next project.",
    authorName: "Mike Chen",
    authorEmail: "<EMAIL>",
    postId: "test-post-2"
  },
  {
    content: "The AI integration trends you mentioned are fascinating. Do you have any recommendations for getting started with AI-powered development tools?",
    authorName: "Emily Rodriguez",
    authorEmail: "<EMAIL>",
    postId: "test-post-3"
  },
  {
    content: "Excellent breakdown of modern web development trends. The section on WebAssembly was particularly interesting - I hadn't considered its potential for web applications.",
    authorName: "David Kim",
    authorEmail: "<EMAIL>",
    postId: "test-post-3"
  }
]

// Utility functions for test data
export const getRandomTestPost = (): TestBlogPost => {
  const posts = generateTestBlogPosts()
  return posts[Math.floor(Math.random() * posts.length)]
}

export const getRandomTestProject = (): TestProject => {
  const projects = generateTestProjects()
  return projects[Math.floor(Math.random() * projects.length)]
}

export const getRandomTestComment = (): TestComment => {
  const comments = generateTestComments()
  return comments[Math.floor(Math.random() * comments.length)]
}

// Generate test data with variations
export const generateVariationTestData = (count: number) => {
  const variations = []
  const basePost = getRandomTestPost()
  
  for (let i = 0; i < count; i++) {
    variations.push({
      ...basePost,
      title: `${basePost.title} - Variation ${i + 1}`,
      publishDate: new Date(Date.now() - (i * 86400000)).toISOString()
    })
  }
  
  return variations
}

// Test data validation
export const validateTestData = (data: any, type: 'blog' | 'project' | 'comment'): boolean => {
  switch (type) {
    case 'blog':
      return !!(data.title && data.content && data.categories && data.tags)
    case 'project':
      return !!(data.title && data.client && data.industry && data.technologyStack)
    case 'comment':
      return !!(data.content && data.authorName && data.authorEmail)
    default:
      return false
  }
}

// Export all test data
export const testData = {
  blogPosts: generateTestBlogPosts(),
  projects: generateTestProjects(),
  comments: generateTestComments()
}
