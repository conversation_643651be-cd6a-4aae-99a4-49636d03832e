// Research Engine for AI Blog Generator
'use client'

import * as cheerio from 'cheerio'
import { ResearchResult, ResearchSource, AIRequest } from '@/types/ai'
import { optimizedAIManager } from './optimization'
import { getOrCreateAISettings } from './settings'

// Research Engine Class
export class ResearchEngine {
  private static instance: ResearchEngine
  private searchCache = new Map<string, ResearchResult>()

  static getInstance(): ResearchEngine {
    if (!ResearchEngine.instance) {
      ResearchEngine.instance = new ResearchEngine()
    }
    return ResearchEngine.instance
  }

  // Main research method
  async conductResearch(
    keywords: string[],
    userId: string,
    options: {
      maxSources?: number
      includeAcademic?: boolean
      includeNews?: boolean
      includeBlog?: boolean
      language?: string
    } = {}
  ): Promise<ResearchResult> {
    const query = keywords.join(' ')
    const cacheKey = this.createCacheKey(query, options)

    // Check cache first
    if (this.searchCache.has(cacheKey)) {
      return this.searchCache.get(cacheKey)!
    }

    try {
      // Get search results from multiple sources
      const sources = await this.gatherSources(keywords, options)
      
      // Process and analyze sources with AI
      const analysis = await this.analyzeSources(sources, query, userId)
      
      const result: ResearchResult = {
        query,
        sources: sources.slice(0, options.maxSources || 10),
        summary: analysis.summary,
        keyPoints: analysis.keyPoints,
        relatedTopics: analysis.relatedTopics,
        tokensUsed: analysis.tokensUsed,
        cost: analysis.cost
      }

      // Cache the result
      this.searchCache.set(cacheKey, result)
      
      return result
    } catch (error) {
      console.error('Research failed:', error)
      throw new Error(`Research failed: ${error}`)
    }
  }

  private async gatherSources(
    keywords: string[],
    options: any
  ): Promise<ResearchSource[]> {
    const sources: ResearchSource[] = []
    
    // Simulate web search (in production, you'd use real search APIs)
    const searchQueries = this.generateSearchQueries(keywords)
    
    for (const query of searchQueries) {
      try {
        // In a real implementation, you would:
        // 1. Use Google Custom Search API
        // 2. Use Bing Search API
        // 3. Use academic databases like arXiv, PubMed
        // 4. Use news APIs
        
        // For now, we'll create mock sources based on common patterns
        const mockSources = await this.generateMockSources(query, options)
        sources.push(...mockSources)
        
        if (sources.length >= (options.maxSources || 10) * 2) {
          break
        }
      } catch (error) {
        console.warn(`Failed to search for: ${query}`, error)
      }
    }

    // Score and filter sources
    return this.scoreAndFilterSources(sources, keywords)
  }

  private generateSearchQueries(keywords: string[]): string[] {
    const queries: string[] = []
    
    // Primary query
    queries.push(keywords.join(' '))
    
    // Individual keyword queries
    keywords.forEach(keyword => {
      queries.push(keyword)
      queries.push(`${keyword} guide`)
      queries.push(`${keyword} tutorial`)
      queries.push(`${keyword} best practices`)
    })
    
    // Combination queries
    if (keywords.length > 1) {
      for (let i = 0; i < keywords.length - 1; i++) {
        queries.push(`${keywords[i]} ${keywords[i + 1]}`)
      }
    }
    
    return queries.slice(0, 5) // Limit to 5 queries
  }

  private async generateMockSources(query: string, options: any): Promise<ResearchSource[]> {
    // This is a mock implementation
    // In production, replace with actual web scraping/API calls
    const mockSources: ResearchSource[] = [
      {
        url: `https://example.com/article-${encodeURIComponent(query)}`,
        title: `Complete Guide to ${query}`,
        content: `This is a comprehensive guide about ${query}. It covers the fundamentals, best practices, and advanced techniques. The content includes practical examples and real-world applications.`,
        relevanceScore: 0.9,
        datePublished: new Date().toISOString(),
        author: 'Expert Author',
        domain: 'example.com'
      },
      {
        url: `https://blog.example.com/${encodeURIComponent(query)}-tips`,
        title: `10 Essential Tips for ${query}`,
        content: `Here are the top 10 tips for mastering ${query}. These insights come from years of experience and industry best practices.`,
        relevanceScore: 0.8,
        datePublished: new Date(Date.now() - 86400000).toISOString(),
        author: 'Industry Expert',
        domain: 'blog.example.com'
      },
      {
        url: `https://research.example.com/study-${encodeURIComponent(query)}`,
        title: `Research Study: ${query} Analysis`,
        content: `A comprehensive research study analyzing ${query}. The study includes statistical data, methodology, and conclusions.`,
        relevanceScore: 0.85,
        datePublished: new Date(Date.now() - 172800000).toISOString(),
        author: 'Research Team',
        domain: 'research.example.com'
      }
    ]

    return mockSources
  }

  private scoreAndFilterSources(sources: ResearchSource[], keywords: string[]): ResearchSource[] {
    // Score sources based on relevance
    const scoredSources = sources.map(source => {
      let score = source.relevanceScore || 0
      
      // Boost score for keyword matches in title
      keywords.forEach(keyword => {
        if (source.title.toLowerCase().includes(keyword.toLowerCase())) {
          score += 0.2
        }
        if (source.content.toLowerCase().includes(keyword.toLowerCase())) {
          score += 0.1
        }
      })
      
      // Boost score for recent content
      if (source.datePublished) {
        const daysSincePublished = (Date.now() - new Date(source.datePublished).getTime()) / (1000 * 60 * 60 * 24)
        if (daysSincePublished < 30) {
          score += 0.1
        }
      }
      
      // Boost score for authoritative domains
      const authoritativeDomains = ['edu', 'gov', 'org']
      if (authoritativeDomains.some(domain => source.domain.includes(domain))) {
        score += 0.15
      }
      
      return { ...source, relevanceScore: Math.min(score, 1.0) }
    })

    // Sort by relevance score and remove duplicates
    return scoredSources
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .filter((source, index, array) => 
        array.findIndex(s => s.url === source.url) === index
      )
  }

  private async analyzeSources(
    sources: ResearchSource[],
    query: string,
    userId: string
  ): Promise<{
    summary: string
    keyPoints: string[]
    relatedTopics: string[]
    tokensUsed: number
    cost: number
  }> {
    const settings = await getOrCreateAISettings(userId)
    
    // Prepare content for analysis
    const sourceContent = sources.map(source => 
      `Source: ${source.title}\nURL: ${source.url}\nContent: ${source.content.substring(0, 500)}...`
    ).join('\n\n')

    const analysisPrompt = `
Analyze the following research sources about "${query}" and provide:

1. A comprehensive summary (2-3 paragraphs)
2. Key points (5-7 bullet points)
3. Related topics for further research (5-6 topics)

Research Sources:
${sourceContent}

Please provide the analysis in the following JSON format:
{
  "summary": "...",
  "keyPoints": ["...", "..."],
  "relatedTopics": ["...", "..."]
}
`

    const request: AIRequest = {
      provider: settings.provider,
      model: settings.models.research,
      prompt: analysisPrompt,
      maxTokens: 2000,
      temperature: 0.3,
      systemPrompt: 'You are a research analyst. Provide accurate, well-structured analysis of research sources.'
    }

    try {
      const response = await optimizedAIManager.generateOptimizedResponse(request, userId, settings)
      
      // Parse the JSON response
      const analysis = JSON.parse(response.content)
      
      return {
        summary: analysis.summary || 'Analysis summary not available',
        keyPoints: analysis.keyPoints || [],
        relatedTopics: analysis.relatedTopics || [],
        tokensUsed: response.tokensUsed,
        cost: response.cost
      }
    } catch (error) {
      console.error('Failed to analyze sources:', error)
      
      // Fallback analysis
      return {
        summary: `Research conducted on "${query}" based on ${sources.length} sources. The sources provide comprehensive information on the topic.`,
        keyPoints: sources.slice(0, 5).map(source => source.title),
        relatedTopics: [`${query} best practices`, `${query} tutorials`, `${query} case studies`],
        tokensUsed: 0,
        cost: 0
      }
    }
  }

  private createCacheKey(query: string, options: any): string {
    return `research:${query}:${JSON.stringify(options)}`
  }

  // Clear cache
  clearCache(): void {
    this.searchCache.clear()
  }

  // Get cached research
  getCachedResearch(query: string, options: any = {}): ResearchResult | null {
    const cacheKey = this.createCacheKey(query, options)
    return this.searchCache.get(cacheKey) || null
  }
}

// Export singleton instance
export const researchEngine = ResearchEngine.getInstance()

// Utility functions
export const conductKeywordResearch = async (
  keywords: string[],
  userId: string,
  options?: any
): Promise<ResearchResult> => {
  return await researchEngine.conductResearch(keywords, userId, options)
}

export const validateSources = (sources: ResearchSource[]): ResearchSource[] => {
  return sources.filter(source => {
    // Basic validation
    if (!source.url || !source.title || !source.content) {
      return false
    }
    
    // URL validation
    try {
      new URL(source.url)
    } catch {
      return false
    }
    
    // Content length validation
    if (source.content.length < 50) {
      return false
    }
    
    return true
  })
}
