'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  DashboardCard,
  DashboardCardContent,
  DashboardCardDescription,
  DashboardCardHeader,
  DashboardCardTitle
} from '@/components/dashboard/DashboardCard'
import {
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XMarkIcon,
  LockClosedIcon
} from '@heroicons/react/24/outline'
import { useCSRFToken, CSRFTokenInput } from '@/lib/csrf'

interface SecurityTestResult {
  name: string
  status: 'pending' | 'pass' | 'fail' | 'warning'
  description: string
  details?: string
  severity?: 'low' | 'medium' | 'high' | 'critical'
}

export default function SecurityTestPage() {
  const { user } = useAuth()
  const { token: csrfToken } = useCSRFToken()
  const [testResults, setTestResults] = useState<SecurityTestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [currentTest, setCurrentTest] = useState('')
  const [testInputs, setTestInputs] = useState({
    xssTest: '<script>alert("XSS")</script>',
    sqlTest: "'; DROP TABLE users; --",
    longText: 'A'.repeat(10000),
    specialChars: '!@#$%^&*()_+{}|:"<>?[]\\;\',./',
    htmlTags: '<img src="x" onerror="alert(1)">',
    jsCode: 'javascript:alert("XSS")'
  })

  const updateResult = (name: string, status: SecurityTestResult['status'], description: string, details?: string, severity?: SecurityTestResult['severity']) => {
    setTestResults(prev => {
      const existing = prev.find(r => r.name === name)
      if (existing) {
        existing.status = status
        existing.description = description
        existing.details = details
        existing.severity = severity
        return [...prev]
      } else {
        return [...prev, { name, status, description, details, severity }]
      }
    })
  }

  const log = (message: string) => {
    console.log(`[Security Test] ${message}`)
  }

  const testInputValidation = async () => {
    setCurrentTest('Testing input validation and sanitization...')
    log('Testing input validation')

    // Test XSS prevention
    try {
      const xssPayloads = [
        '<script>alert("XSS")</script>',
        '<img src="x" onerror="alert(1)">',
        'javascript:alert("XSS")',
        '<svg onload="alert(1)">',
        '"><script>alert("XSS")</script>'
      ]

      let xssBlocked = 0
      for (const payload of xssPayloads) {
        // Test if the payload gets sanitized when displayed
        const testDiv = document.createElement('div')
        testDiv.innerHTML = payload

        // More comprehensive checks
        const hasScript = testDiv.innerHTML.includes('<script')
        const hasOnError = testDiv.innerHTML.includes('onerror')
        const hasJavascript = testDiv.innerHTML.includes('javascript:')
        const hasOnLoad = testDiv.innerHTML.includes('onload')
        const hasSvg = testDiv.innerHTML.includes('<svg') && testDiv.innerHTML.includes('onload')

        if (!hasScript && !hasOnError && !hasJavascript && !hasOnLoad && !hasSvg) {
          xssBlocked++
        }
      }

      const xssStatus = xssBlocked === xssPayloads.length ? 'pass' : xssBlocked > 0 ? 'warning' : 'fail'
      updateResult(
        'XSS Prevention',
        xssStatus,
        `${xssBlocked}/${xssPayloads.length} XSS payloads blocked`,
        'Tests if dangerous scripts are sanitized',
        xssStatus === 'fail' ? 'critical' : xssStatus === 'warning' ? 'high' : 'low'
      )
    } catch (error) {
      updateResult('XSS Prevention', 'fail', 'Error testing XSS prevention', error instanceof Error ? error.message : 'Unknown error', 'critical')
    }

    // Test SQL injection patterns (client-side detection)
    const sqlPatterns = [
      "'; DROP TABLE",
      "' OR '1'='1",
      "UNION SELECT",
      "'; DELETE FROM",
      "' OR 1=1 --"
    ]

    updateResult(
      'SQL Injection Patterns',
      'pass',
      'Client-side SQL pattern detection',
      'SQL injection should be prevented server-side',
      'medium'
    )

    // Test file upload validation
    updateResult(
      'File Upload Validation',
      'warning',
      'Manual testing required',
      'Test file upload with malicious files (.exe, .php, oversized files)',
      'high'
    )
  }

  const testAuthentication = async () => {
    setCurrentTest('Testing authentication and authorization...')
    log('Testing authentication security')

    // Test if user is properly authenticated
    if (user) {
      updateResult(
        'User Authentication',
        'pass',
        'User is properly authenticated',
        `User ID: ${user.uid}`,
        'low'
      )

      // Test admin access
      const isAdmin = user.uid === 'KNlrg408xubJeEmwFpUbeDQWBgF3'
      updateResult(
        'Admin Access Control',
        isAdmin ? 'pass' : 'fail',
        isAdmin ? 'Admin access granted correctly' : 'Non-admin user accessing admin area',
        `Admin check: ${isAdmin}`,
        isAdmin ? 'low' : 'critical'
      )
    } else {
      updateResult(
        'User Authentication',
        'fail',
        'User not authenticated but accessing protected area',
        'This should not happen',
        'critical'
      )
    }

    // Test session security
    const hasSecureCookies = document.cookie.includes('Secure') || document.cookie.includes('SameSite')
    updateResult(
      'Session Security',
      hasSecureCookies ? 'pass' : 'warning',
      hasSecureCookies ? 'Secure cookie attributes detected' : 'No secure cookie attributes found',
      'Check for Secure and SameSite cookie attributes',
      'medium'
    )
  }

  const testAPIEndpoints = async () => {
    setCurrentTest('Testing API endpoint security...')
    log('Testing API security')

    const testEndpoints = [
      { url: '/api/posts', method: 'GET', expectAuth: false },
      { url: '/api/posts', method: 'POST', expectAuth: true },
      { url: '/api/admin/users', method: 'GET', expectAuth: true },
      { url: '/api/upload', method: 'POST', expectAuth: true }
    ]

    for (const endpoint of testEndpoints) {
      try {
        const response = await fetch(endpoint.url, {
          method: endpoint.method,
          headers: {
            'Content-Type': 'application/json'
          }
        })

        const isProtected = response.status === 401 || response.status === 403
        const shouldBeProtected = endpoint.expectAuth

        if (shouldBeProtected && isProtected) {
          updateResult(
            `API Protection: ${endpoint.method} ${endpoint.url}`,
            'pass',
            'Endpoint properly protected',
            `Status: ${response.status}`,
            'low'
          )
        } else if (!shouldBeProtected && !isProtected) {
          updateResult(
            `API Access: ${endpoint.method} ${endpoint.url}`,
            'pass',
            'Public endpoint accessible',
            `Status: ${response.status}`,
            'low'
          )
        } else {
          updateResult(
            `API Security: ${endpoint.method} ${endpoint.url}`,
            'fail',
            shouldBeProtected ? 'Protected endpoint not secured' : 'Public endpoint blocked',
            `Status: ${response.status}`,
            'high'
          )
        }
      } catch (error) {
        updateResult(
          `API Test: ${endpoint.method} ${endpoint.url}`,
          'warning',
          'Endpoint test failed',
          error instanceof Error ? error.message : 'Unknown error',
          'medium'
        )
      }
    }
  }

  const testDataValidation = async () => {
    setCurrentTest('Testing data validation...')
    log('Testing data validation')

    // Test form validation
    const forms = document.querySelectorAll('form')
    updateResult(
      'Form Validation',
      forms.length > 0 ? 'pass' : 'warning',
      `${forms.length} forms found on page`,
      'Forms should have proper validation',
      'medium'
    )

    // Test required field validation
    const requiredFields = document.querySelectorAll('[required]')
    updateResult(
      'Required Field Validation',
      requiredFields.length > 0 ? 'pass' : 'warning',
      `${requiredFields.length} required fields found`,
      'Required fields should prevent empty submission',
      'medium'
    )

    // Test input length limits
    const textInputs = document.querySelectorAll('input[type="text"], textarea')
    let hasLimits = 0
    textInputs.forEach(input => {
      if (input.hasAttribute('maxlength') || input.hasAttribute('minlength')) {
        hasLimits++
      }
    })

    updateResult(
      'Input Length Validation',
      hasLimits > 0 ? 'pass' : 'warning',
      `${hasLimits}/${textInputs.length} inputs have length limits`,
      'Inputs should have appropriate length limits',
      'medium'
    )
  }

  const testCSRFProtection = async () => {
    setCurrentTest('Testing CSRF protection...')
    log('Testing CSRF protection')

    // Check for CSRF tokens in forms and session storage
    const csrfTokens = document.querySelectorAll('input[name*="csrf"], input[name*="token"]')
    const sessionToken = sessionStorage.getItem('csrf_token')
    const hasCSRFProtection = csrfTokens.length > 0 || sessionToken || csrfToken

    updateResult(
      'CSRF Token Protection',
      hasCSRFProtection ? 'pass' : 'warning',
      hasCSRFProtection ? 'CSRF protection detected' : 'No CSRF tokens detected',
      hasCSRFProtection ? 'CSRF tokens found in session or forms' : 'Forms should include CSRF protection',
      'high'
    )

    // Test SameSite cookie attribute
    const cookies = document.cookie
    const hasSameSite = cookies.includes('SameSite')
    updateResult(
      'SameSite Cookie Protection',
      hasSameSite ? 'pass' : 'warning',
      hasSameSite ? 'SameSite attribute detected' : 'No SameSite attribute found',
      'Cookies should have SameSite attribute for CSRF protection',
      'medium'
    )
  }

  const runAllSecurityTests = async () => {
    setIsRunning(true)
    setTestResults([])
    log('Starting comprehensive security test suite')

    try {
      await testAuthentication()
      await new Promise(resolve => setTimeout(resolve, 500))
      
      await testInputValidation()
      await new Promise(resolve => setTimeout(resolve, 500))
      
      await testAPIEndpoints()
      await new Promise(resolve => setTimeout(resolve, 500))
      
      await testDataValidation()
      await new Promise(resolve => setTimeout(resolve, 500))
      
      await testCSRFProtection()
      
      setCurrentTest('Security tests completed!')
      log('Security test suite completed')
    } catch (error) {
      log(`Test suite error: ${error}`)
    } finally {
      setIsRunning(false)
    }
  }

  const getStatusIcon = (status: SecurityTestResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />
      case 'fail':
        return <XMarkIcon className="w-5 h-5 text-red-500" />
      case 'warning':
        return <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500" />
      default:
        return <LockClosedIcon className="w-5 h-5 text-gray-400" />
    }
  }

  const getSeverityColor = (severity?: SecurityTestResult['severity']) => {
    switch (severity) {
      case 'critical': return 'text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20'
      case 'high': return 'text-orange-600 dark:text-orange-400 bg-orange-50 dark:bg-orange-900/20'
      case 'medium': return 'text-yellow-600 dark:text-yellow-400 bg-yellow-50 dark:bg-yellow-900/20'
      case 'low': return 'text-green-600 dark:text-green-400 bg-green-50 dark:bg-green-900/20'
      default: return 'text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-900/20'
    }
  }

  const passedTests = testResults.filter(r => r.status === 'pass').length
  const failedTests = testResults.filter(r => r.status === 'fail').length
  const warningTests = testResults.filter(r => r.status === 'warning').length
  const criticalIssues = testResults.filter(r => r.severity === 'critical').length

  return (
    <div className="space-y-6">
      {/* Hidden CSRF Token for testing */}
      <CSRFTokenInput />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight dashboard-text flex items-center gap-2">
            <ShieldCheckIcon className="w-7 h-7 text-blue-600" />
            Security & Data Validation Testing
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Comprehensive security testing and vulnerability assessment
          </p>
        </div>
        <Button 
          onClick={runAllSecurityTests} 
          disabled={isRunning}
          className="px-6"
        >
          {isRunning ? 'Running Tests...' : 'Run Security Tests'}
        </Button>
      </div>

      {/* Current Test Status */}
      {isRunning && (
        <DashboardCard>
          <DashboardCardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="dashboard-text">{currentTest}</span>
            </div>
          </DashboardCardContent>
        </DashboardCard>
      )}

      {/* Security Summary */}
      {testResults.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <DashboardCard>
            <DashboardCardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-green-600">{passedTests}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Passed</div>
            </DashboardCardContent>
          </DashboardCard>
          <DashboardCard>
            <DashboardCardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-yellow-600">{warningTests}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Warnings</div>
            </DashboardCardContent>
          </DashboardCard>
          <DashboardCard>
            <DashboardCardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-red-600">{failedTests}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Failed</div>
            </DashboardCardContent>
          </DashboardCard>
          <DashboardCard>
            <DashboardCardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-red-600">{criticalIssues}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Critical</div>
            </DashboardCardContent>
          </DashboardCard>
        </div>
      )}

      {/* Test Results */}
      {testResults.length > 0 && (
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle>Security Test Results</DashboardCardTitle>
            <DashboardCardDescription>
              Detailed security assessment and vulnerability findings
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            <div className="space-y-4">
              {testResults.map((result, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3 flex-1">
                      {getStatusIcon(result.status)}
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="font-medium dashboard-text">{result.name}</span>
                          {result.severity && (
                            <span className={`px-2 py-1 text-xs rounded-full ${getSeverityColor(result.severity)}`}>
                              {result.severity.toUpperCase()}
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                          {result.description}
                        </p>
                        {result.details && (
                          <p className="text-xs text-gray-500 dark:text-gray-500 mt-2">
                            {result.details}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </DashboardCardContent>
        </DashboardCard>
      )}
    </div>
  )
}
