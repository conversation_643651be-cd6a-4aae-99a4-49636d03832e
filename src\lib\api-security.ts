import { NextRequest, NextResponse } from 'next/server'
import { verifyIdToken } from '@/lib/firebase-admin'

// Rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>()

interface RateLimitConfig {
  maxRequests: number
  windowMs: number
}

interface SecurityConfig {
  requireAuth?: boolean
  requireAdmin?: boolean
  rateLimit?: RateLimitConfig
  allowedMethods?: string[]
  validateInput?: boolean
}

const ADMIN_UID = 'KNlrg408xubJeEmwFpUbeDQWBgF3'

/**
 * Check rate limiting for a user
 */
export function checkRateLimit(userId: string, config: RateLimitConfig) {
  const now = Date.now()
  const userLimit = rateLimitStore.get(userId)

  if (!userLimit || now > userLimit.resetTime) {
    // Reset or create new limit
    rateLimitStore.set(userId, {
      count: 1,
      resetTime: now + config.windowMs
    })
    return {
      allowed: true,
      remaining: config.maxRequests - 1,
      resetTime: now + config.windowMs
    }
  }

  if (userLimit.count >= config.maxRequests) {
    return {
      allowed: false,
      remaining: 0,
      resetTime: userLimit.resetTime
    }
  }

  userLimit.count++
  return {
    allowed: true,
    remaining: config.maxRequests - userLimit.count,
    resetTime: userLimit.resetTime
  }
}

/**
 * Validate request method
 */
export function validateMethod(request: NextRequest, allowedMethods: string[]) {
  return allowedMethods.includes(request.method)
}

/**
 * Extract and verify Firebase token
 */
export async function verifyAuthentication(request: NextRequest) {
  const authorization = request.headers.get('authorization')
  
  if (!authorization?.startsWith('Bearer ')) {
    return { success: false, error: 'Missing or invalid authorization header' }
  }

  const token = authorization.split('Bearer ')[1]
  
  try {
    const decodedToken = await verifyIdToken(token)
    return { success: true, user: decodedToken }
  } catch (error) {
    return { success: false, error: 'Invalid token' }
  }
}

/**
 * Check if user is admin
 */
export function verifyAdmin(userId: string) {
  return userId === ADMIN_UID
}

/**
 * Validate request input
 */
export function validateInput(data: any, requiredFields: string[] = []) {
  const errors: string[] = []

  // Check required fields
  for (const field of requiredFields) {
    if (!data[field]) {
      errors.push(`Missing required field: ${field}`)
    }
  }

  // Check for potentially dangerous content
  const dangerousPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+\s*=/i,
    /<iframe/i,
    /vbscript:/i
  ]

  const checkValue = (value: any, path: string = '') => {
    if (typeof value === 'string') {
      for (const pattern of dangerousPatterns) {
        if (pattern.test(value)) {
          errors.push(`Potentially dangerous content detected in ${path || 'input'}`)
          break
        }
      }
    } else if (typeof value === 'object' && value !== null) {
      for (const [key, val] of Object.entries(value)) {
        checkValue(val, path ? `${path}.${key}` : key)
      }
    }
  }

  checkValue(data)

  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * Security middleware wrapper for API routes
 */
export function withSecurity(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>,
  config: SecurityConfig = {}
) {
  return async (request: NextRequest, context?: any) => {
    try {
      // Method validation
      if (config.allowedMethods && !validateMethod(request, config.allowedMethods)) {
        return NextResponse.json(
          { error: `Method ${request.method} not allowed` },
          { status: 405 }
        )
      }

      let userId: string | undefined

      // Authentication check
      if (config.requireAuth || config.requireAdmin) {
        const authResult = await verifyAuthentication(request)
        if (!authResult.success) {
          return NextResponse.json(
            { error: authResult.error },
            { status: 401 }
          )
        }
        userId = authResult.user.uid

        // Admin check
        if (config.requireAdmin && !verifyAdmin(userId)) {
          return NextResponse.json(
            { error: 'Admin access required' },
            { status: 403 }
          )
        }
      }

      // Rate limiting
      if (config.rateLimit && userId) {
        const rateLimit = checkRateLimit(userId, config.rateLimit)
        if (!rateLimit.allowed) {
          return NextResponse.json(
            { 
              error: 'Rate limit exceeded',
              resetTime: rateLimit.resetTime
            },
            { 
              status: 429,
              headers: {
                'X-RateLimit-Limit': config.rateLimit.maxRequests.toString(),
                'X-RateLimit-Remaining': '0',
                'X-RateLimit-Reset': rateLimit.resetTime.toString()
              }
            }
          )
        }
      }

      // Input validation
      if (config.validateInput && ['POST', 'PUT', 'PATCH'].includes(request.method)) {
        try {
          const body = await request.json()
          const validation = validateInput(body)
          if (!validation.valid) {
            return NextResponse.json(
              { 
                error: 'Invalid input',
                details: validation.errors
              },
              { status: 400 }
            )
          }
        } catch (error) {
          return NextResponse.json(
            { error: 'Invalid JSON body' },
            { status: 400 }
          )
        }
      }

      // Call the actual handler
      return await handler(request, context)

    } catch (error) {
      console.error('Security middleware error:', error)
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

/**
 * Common security configurations
 */
export const SecurityConfigs = {
  // Public endpoints (no auth required)
  public: {
    allowedMethods: ['GET'],
    validateInput: false
  },

  // Protected endpoints (auth required)
  protected: {
    requireAuth: true,
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE'],
    validateInput: true,
    rateLimit: {
      maxRequests: 100,
      windowMs: 60 * 60 * 1000 // 1 hour
    }
  },

  // Admin endpoints (admin auth required)
  admin: {
    requireAuth: true,
    requireAdmin: true,
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE'],
    validateInput: true,
    rateLimit: {
      maxRequests: 200,
      windowMs: 60 * 60 * 1000 // 1 hour
    }
  },

  // AI endpoints (stricter rate limiting)
  ai: {
    requireAuth: true,
    allowedMethods: ['GET', 'POST'],
    validateInput: true,
    rateLimit: {
      maxRequests: 10,
      windowMs: 60 * 60 * 1000 // 1 hour
    }
  }
}

// Export individual functions for custom usage
export {
  verifyAuthentication,
  verifyAdmin,
  validateInput,
  validateMethod,
  checkRateLimit
}
