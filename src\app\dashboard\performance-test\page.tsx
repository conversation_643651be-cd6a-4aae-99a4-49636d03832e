'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { Button } from '@/components/ui/button'
import {
  DashboardCard,
  DashboardCardContent,
  DashboardCardDescription,
  DashboardCardHeader,
  DashboardCardTitle
} from '@/components/dashboard/DashboardCard'
import {
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon
} from '@heroicons/react/24/outline'

interface TestResult {
  name: string
  status: 'pending' | 'pass' | 'fail' | 'warning'
  value?: string
  message?: string
}

export default function PerformanceTestPage() {
  const { user } = useAuth()
  const [testResults, setTestResults] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [currentTest, setCurrentTest] = useState('')

  const updateResult = (name: string, status: TestResult['status'], value?: string, message?: string) => {
    setTestResults(prev => {
      const existing = prev.find(r => r.name === name)
      if (existing) {
        existing.status = status
        existing.value = value
        existing.message = message
        return [...prev]
      } else {
        return [...prev, { name, status, value, message }]
      }
    })
  }

  const log = (message: string) => {
    console.log(`[Performance Test] ${message}`)
  }

  const testPageLoadPerformance = async () => {
    setCurrentTest('Testing page load performance...')
    log('Testing page load performance')

    const testUrls = [
      '/',
      '/blog',
      '/projects',
      '/dashboard',
      '/dashboard/posts',
      '/dashboard/ai-generator'
    ]

    for (const url of testUrls) {
      try {
        const startTime = performance.now()
        const response = await fetch(url, { method: 'HEAD' })
        const endTime = performance.now()
        const duration = endTime - startTime

        const status = duration < 500 ? 'pass' : duration < 1000 ? 'warning' : 'fail'
        updateResult(`Page Load: ${url}`, status, `${duration.toFixed(2)}ms`)
        log(`${url}: ${duration.toFixed(2)}ms`)
      } catch (error) {
        updateResult(`Page Load: ${url}`, 'fail', 'Failed', error instanceof Error ? error.message : 'Unknown error')
        log(`${url}: Failed - ${error}`)
      }
    }
  }

  const testCoreWebVitals = async () => {
    setCurrentTest('Testing Core Web Vitals...')
    log('Testing Core Web Vitals')

    // Navigation Timing API
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    if (navigation) {
      const loadTime = navigation.loadEventEnd - navigation.fetchStart
      const domContentLoaded = navigation.domContentLoadedEventEnd - navigation.fetchStart
      const firstByte = navigation.responseStart - navigation.fetchStart

      updateResult('Load Time', loadTime < 2000 ? 'pass' : loadTime < 4000 ? 'warning' : 'fail', `${loadTime.toFixed(2)}ms`)
      updateResult('DOM Content Loaded', domContentLoaded < 1500 ? 'pass' : 'warning', `${domContentLoaded.toFixed(2)}ms`)
      updateResult('Time to First Byte', firstByte < 600 ? 'pass' : firstByte < 1000 ? 'warning' : 'fail', `${firstByte.toFixed(2)}ms`)
    }

    // Resource loading
    const resources = performance.getEntriesByType('resource')
    const totalSize = resources.reduce((sum, resource) => {
      return sum + (resource as any).transferSize || 0
    }, 0)

    const slowResources = resources.filter(resource => {
      const duration = resource.responseEnd - resource.requestStart
      return duration > 1000
    }).length

    updateResult('Total Resources', 'pass', resources.length.toString())
    updateResult('Total Transfer Size', totalSize < 2000000 ? 'pass' : 'warning', `${(totalSize / 1024).toFixed(2)} KB`)
    updateResult('Slow Resources', slowResources === 0 ? 'pass' : slowResources < 5 ? 'warning' : 'fail', slowResources.toString())
  }

  const testMemoryUsage = async () => {
    setCurrentTest('Testing memory usage...')
    log('Testing memory usage')

    if ('memory' in performance) {
      const memory = (performance as any).memory
      const usedMemory = memory.usedJSHeapSize / 1024 / 1024 // MB
      const totalMemory = memory.totalJSHeapSize / 1024 / 1024 // MB

      updateResult('Used Memory', usedMemory < 50 ? 'pass' : usedMemory < 100 ? 'warning' : 'fail', `${usedMemory.toFixed(2)} MB`)
      updateResult('Total Memory', 'pass', `${totalMemory.toFixed(2)} MB`)
    } else {
      updateResult('Memory Usage', 'warning', 'Not available', 'Memory API not supported in this browser')
    }
  }

  const testErrorHandling = async () => {
    setCurrentTest('Testing error handling...')
    log('Testing error handling')

    // Test invalid API endpoint
    try {
      await fetch('/api/nonexistent-endpoint')
      updateResult('Invalid API Handling', 'warning', 'No error thrown')
    } catch (error) {
      updateResult('Invalid API Handling', 'pass', 'Error caught correctly')
    }

    // Test large data handling
    try {
      const largeData = 'x'.repeat(1000000) // 1MB string
      const processed = largeData.length
      updateResult('Large Data Handling', 'pass', `${processed} characters processed`)
    } catch (error) {
      updateResult('Large Data Handling', 'fail', 'Failed to process large data')
    }

    // Test localStorage limits
    try {
      const testKey = 'perf-test-storage'
      const largeData = JSON.stringify({ data: 'x'.repeat(100000) })
      localStorage.setItem(testKey, largeData)
      localStorage.removeItem(testKey)
      updateResult('LocalStorage Handling', 'pass', 'Large data stored/retrieved')
    } catch (error) {
      updateResult('LocalStorage Handling', 'warning', 'Storage limit reached')
    }
  }

  const runAllTests = async () => {
    setIsRunning(true)
    setTestResults([])
    log('Starting comprehensive performance test suite')

    try {
      await testCoreWebVitals()
      await new Promise(resolve => setTimeout(resolve, 500))
      
      await testPageLoadPerformance()
      await new Promise(resolve => setTimeout(resolve, 500))
      
      await testMemoryUsage()
      await new Promise(resolve => setTimeout(resolve, 500))
      
      await testErrorHandling()
      
      setCurrentTest('All tests completed!')
      log('Performance test suite completed')
    } catch (error) {
      log(`Test suite error: ${error}`)
    } finally {
      setIsRunning(false)
    }
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />
      case 'fail':
        return <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />
      case 'warning':
        return <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500" />
      default:
        return <ClockIcon className="w-5 h-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pass': return 'text-green-600 dark:text-green-400'
      case 'fail': return 'text-red-600 dark:text-red-400'
      case 'warning': return 'text-yellow-600 dark:text-yellow-400'
      default: return 'text-gray-600 dark:text-gray-400'
    }
  }

  const passedTests = testResults.filter(r => r.status === 'pass').length
  const failedTests = testResults.filter(r => r.status === 'fail').length
  const warningTests = testResults.filter(r => r.status === 'warning').length

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight dashboard-text flex items-center gap-2">
            <ChartBarIcon className="w-7 h-7 text-blue-600" />
            Performance & Error Testing
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Comprehensive testing of application performance and error handling
          </p>
        </div>
        <Button 
          onClick={runAllTests} 
          disabled={isRunning}
          className="px-6"
        >
          {isRunning ? 'Running Tests...' : 'Run All Tests'}
        </Button>
      </div>

      {/* Current Test Status */}
      {isRunning && (
        <DashboardCard>
          <DashboardCardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              <span className="dashboard-text">{currentTest}</span>
            </div>
          </DashboardCardContent>
        </DashboardCard>
      )}

      {/* Test Results Summary */}
      {testResults.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <DashboardCard>
            <DashboardCardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-green-600">{passedTests}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Passed</div>
            </DashboardCardContent>
          </DashboardCard>
          <DashboardCard>
            <DashboardCardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-yellow-600">{warningTests}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Warnings</div>
            </DashboardCardContent>
          </DashboardCard>
          <DashboardCard>
            <DashboardCardContent className="p-6 text-center">
              <div className="text-2xl font-bold text-red-600">{failedTests}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Failed</div>
            </DashboardCardContent>
          </DashboardCard>
          <DashboardCard>
            <DashboardCardContent className="p-6 text-center">
              <div className="text-2xl font-bold dashboard-text">{testResults.length}</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Total Tests</div>
            </DashboardCardContent>
          </DashboardCard>
        </div>
      )}

      {/* Detailed Test Results */}
      {testResults.length > 0 && (
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle>Test Results</DashboardCardTitle>
            <DashboardCardDescription>
              Detailed performance and error handling test results
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(result.status)}
                    <span className="font-medium dashboard-text">{result.name}</span>
                  </div>
                  <div className="text-right">
                    {result.value && (
                      <div className={`font-mono text-sm ${getStatusColor(result.status)}`}>
                        {result.value}
                      </div>
                    )}
                    {result.message && (
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        {result.message}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </DashboardCardContent>
        </DashboardCard>
      )}
    </div>
  )
}
