'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { 
  ChartBarIcon, 
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  CurrencyDollarIcon,
  BoltIcon
} from '@heroicons/react/24/outline'
import {
  DashboardCard,
  DashboardCardContent,
  DashboardCardDescription,
  DashboardCardHeader,
  DashboardCardTitle
} from '@/components/dashboard/DashboardCard'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  getUserPerformanceStats, 
  getUserAlerts, 
  getPerformanceInsights,
  performanceMonitor 
} from '@/lib/ai/performance-monitor'
import { getErrorStats } from '@/lib/ai/error-handler'

export default function AIMonitoringPage() {
  const { user } = useAuth()
  const [stats, setStats] = useState<any>(null)
  const [alerts, setAlerts] = useState<any[]>([])
  const [insights, setInsights] = useState<any>(null)
  const [errorStats, setErrorStats] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState<'24h' | '7d' | '30d'>('24h')

  useEffect(() => {
    if (user) {
      loadMonitoringData()
    }
  }, [user, timeRange])

  const loadMonitoringData = async () => {
    if (!user) return

    try {
      const now = Date.now()
      const timeRanges = {
        '24h': now - 24 * 60 * 60 * 1000,
        '7d': now - 7 * 24 * 60 * 60 * 1000,
        '30d': now - 30 * 24 * 60 * 60 * 1000
      }

      const range = { start: timeRanges[timeRange], end: now }
      
      const [userStats, userAlerts, userInsights, userErrorStats] = await Promise.all([
        getUserPerformanceStats(user.uid, range),
        getUserAlerts(user.uid),
        getPerformanceInsights(user.uid),
        getErrorStats(user.uid)
      ])

      setStats(userStats)
      setAlerts(userAlerts)
      setInsights(userInsights)
      setErrorStats(userErrorStats)
    } catch (error) {
      console.error('Error loading monitoring data:', error)
    } finally {
      setLoading(false)
    }
  }

  const resolveAlert = async (alertId: string) => {
    performanceMonitor.resolveAlert(alertId)
    await loadMonitoringData()
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
      case 'low': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading monitoring data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight dashboard-text flex items-center gap-2">
            <ChartBarIcon className="w-7 h-7 text-blue-600" />
            AI Performance Monitoring
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Monitor AI operations, costs, and system health
          </p>
        </div>
        <div className="flex gap-2">
          {(['24h', '7d', '30d'] as const).map((range) => (
            <Button
              key={range}
              variant={timeRange === range ? 'default' : 'outline'}
              size="sm"
              onClick={() => setTimeRange(range)}
            >
              {range}
            </Button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <DashboardCard>
          <DashboardCardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <BoltIcon className="w-6 h-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Operations</p>
                <p className="text-2xl font-bold dashboard-text">{stats?.totalOperations || 0}</p>
              </div>
            </div>
          </DashboardCardContent>
        </DashboardCard>

        <DashboardCard>
          <DashboardCardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <CheckCircleIcon className="w-6 h-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Success Rate</p>
                <p className="text-2xl font-bold dashboard-text">
                  {stats ? `${(stats.successRate * 100).toFixed(1)}%` : '0%'}
                </p>
              </div>
            </div>
          </DashboardCardContent>
        </DashboardCard>

        <DashboardCard>
          <DashboardCardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <ClockIcon className="w-6 h-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg Duration</p>
                <p className="text-2xl font-bold dashboard-text">
                  {stats ? `${(stats.averageDuration / 1000).toFixed(1)}s` : '0s'}
                </p>
              </div>
            </div>
          </DashboardCardContent>
        </DashboardCard>

        <DashboardCard>
          <DashboardCardContent className="p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
                <CurrencyDollarIcon className="w-6 h-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Cost</p>
                <p className="text-2xl font-bold dashboard-text">
                  ${stats?.totalCost?.toFixed(2) || '0.00'}
                </p>
              </div>
            </div>
          </DashboardCardContent>
        </DashboardCard>
      </div>

      {/* Alerts */}
      {alerts.length > 0 && (
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle className="flex items-center gap-2">
              <ExclamationTriangleIcon className="w-5 h-5 text-orange-500" />
              Active Alerts
            </DashboardCardTitle>
            <DashboardCardDescription>
              Performance and cost alerts requiring attention
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            <div className="space-y-3">
              {alerts.slice(0, 5).map((alert) => (
                <div key={alert.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Badge className={getSeverityColor(alert.severity)}>
                      {alert.severity}
                    </Badge>
                    <div>
                      <p className="text-sm font-medium">{alert.message}</p>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        {new Date(alert.timestamp).toLocaleString()}
                      </p>
                    </div>
                  </div>
                  {!alert.resolved && (
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => resolveAlert(alert.id)}
                    >
                      Resolve
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </DashboardCardContent>
        </DashboardCard>
      )}

      {/* Performance Insights */}
      {insights && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <DashboardCard>
            <DashboardCardHeader>
              <DashboardCardTitle>Recommendations</DashboardCardTitle>
              <DashboardCardDescription>
                AI-powered suggestions to improve performance
              </DashboardCardDescription>
            </DashboardCardHeader>
            <DashboardCardContent>
              {insights.recommendations.length > 0 ? (
                <ul className="space-y-2">
                  {insights.recommendations.map((rec: string, index: number) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-sm">{rec}</span>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  No recommendations at this time. Your AI usage looks optimal!
                </p>
              )}
            </DashboardCardContent>
          </DashboardCard>

          <DashboardCard>
            <DashboardCardHeader>
              <DashboardCardTitle>Top Issues</DashboardCardTitle>
              <DashboardCardDescription>
                Areas that need attention
              </DashboardCardDescription>
            </DashboardCardHeader>
            <DashboardCardContent>
              {insights.topIssues.length > 0 ? (
                <ul className="space-y-2">
                  {insights.topIssues.map((issue: string, index: number) => (
                    <li key={index} className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                      <span className="text-sm">{issue}</span>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  No issues detected. System is running smoothly!
                </p>
              )}
            </DashboardCardContent>
          </DashboardCard>
        </div>
      )}

      {/* Usage Breakdown */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle>Operations by Type</DashboardCardTitle>
            <DashboardCardDescription>
              Breakdown of AI operations
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            {stats?.operationsByType && Object.keys(stats.operationsByType).length > 0 ? (
              <div className="space-y-3">
                {Object.entries(stats.operationsByType).map(([type, count]) => (
                  <div key={type} className="flex items-center justify-between">
                    <span className="text-sm font-medium capitalize">{type}</span>
                    <Badge variant="outline">{count}</Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-600 dark:text-gray-400">
                No operations in selected time range
              </p>
            )}
          </DashboardCardContent>
        </DashboardCard>

        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle>Cost by Provider</DashboardCardTitle>
            <DashboardCardDescription>
              AI provider cost breakdown
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            {stats?.costByProvider && Object.keys(stats.costByProvider).length > 0 ? (
              <div className="space-y-3">
                {Object.entries(stats.costByProvider).map(([provider, cost]) => (
                  <div key={provider} className="flex items-center justify-between">
                    <span className="text-sm font-medium capitalize">{provider}</span>
                    <Badge variant="outline">${(cost as number).toFixed(3)}</Badge>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-600 dark:text-gray-400">
                No cost data in selected time range
              </p>
            )}
          </DashboardCardContent>
        </DashboardCard>
      </div>

      {/* Error Statistics */}
      {errorStats && errorStats.totalErrors > 0 && (
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle>Error Statistics</DashboardCardTitle>
            <DashboardCardDescription>
              Recent errors and their frequency
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-3">Errors by Code</h4>
                <div className="space-y-2">
                  {Object.entries(errorStats.errorsByCode).map(([code, count]) => (
                    <div key={code} className="flex items-center justify-between">
                      <span className="text-sm">{code}</span>
                      <Badge variant="outline" className="bg-red-50 text-red-700">
                        {count}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="font-medium mb-3">Errors by Provider</h4>
                <div className="space-y-2">
                  {Object.entries(errorStats.errorsByProvider).map(([provider, count]) => (
                    <div key={provider} className="flex items-center justify-between">
                      <span className="text-sm capitalize">{provider}</span>
                      <Badge variant="outline" className="bg-red-50 text-red-700">
                        {count}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </DashboardCardContent>
        </DashboardCard>
      )}
    </div>
  )
}
