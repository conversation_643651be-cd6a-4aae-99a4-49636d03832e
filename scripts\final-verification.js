// Final Verification Script for Browser Console
// Run this in the browser console to perform additional verification checks

console.log('🧪 Starting Final Verification Checks...')

// Verification functions
const verificationChecks = {
  // Check if all required components are loaded
  checkComponentsLoaded: () => {
    const checks = {
      'React': typeof React !== 'undefined',
      'Next.js Router': typeof window.__NEXT_DATA__ !== 'undefined',
      'Firebase': typeof window.firebase !== 'undefined' || typeof window.firebaseConfig !== 'undefined',
      'Tailwind CSS': document.querySelector('style[data-emotion]') !== null || 
                     document.querySelector('link[href*="tailwind"]') !== null ||
                     getComputedStyle(document.body).getPropertyValue('--tw-bg-opacity') !== '',
      'DOMPurify': typeof window.DOMPurify !== 'undefined'
    }
    
    console.log('📦 Component Loading Checks:')
    Object.entries(checks).forEach(([name, loaded]) => {
      console.log(`  ${loaded ? '✅' : '❌'} ${name}: ${loaded ? 'Loaded' : 'Not Found'}`)
    })
    
    return checks
  },

  // Check page performance
  checkPerformance: () => {
    if (window.performance && window.performance.timing) {
      const timing = window.performance.timing
      const loadTime = timing.loadEventEnd - timing.navigationStart
      const domReady = timing.domContentLoadedEventEnd - timing.navigationStart
      const firstPaint = window.performance.getEntriesByType('paint')
        .find(entry => entry.name === 'first-contentful-paint')?.startTime || 0

      console.log('⚡ Performance Metrics:')
      console.log(`  📊 Total Load Time: ${loadTime}ms`)
      console.log(`  📊 DOM Ready: ${domReady}ms`)
      console.log(`  📊 First Contentful Paint: ${firstPaint.toFixed(2)}ms`)
      
      const performance = {
        loadTime,
        domReady,
        firstPaint,
        grade: loadTime < 3000 ? 'Excellent' : loadTime < 5000 ? 'Good' : 'Needs Improvement'
      }
      
      console.log(`  🎯 Performance Grade: ${performance.grade}`)
      return performance
    }
    
    console.log('❌ Performance API not available')
    return null
  },

  // Check accessibility features
  checkAccessibility: () => {
    const checks = {
      'Alt text on images': Array.from(document.images).every(img => img.alt !== ''),
      'Form labels': Array.from(document.querySelectorAll('input, textarea, select'))
        .every(input => input.labels?.length > 0 || input.getAttribute('aria-label')),
      'Heading hierarchy': (() => {
        const headings = Array.from(document.querySelectorAll('h1, h2, h3, h4, h5, h6'))
        return headings.length > 0
      })(),
      'Focus indicators': getComputedStyle(document.body).getPropertyValue('outline') !== 'none',
      'ARIA attributes': document.querySelectorAll('[aria-label], [aria-describedby], [role]').length > 0
    }
    
    console.log('♿ Accessibility Checks:')
    Object.entries(checks).forEach(([name, passed]) => {
      console.log(`  ${passed ? '✅' : '⚠️'} ${name}: ${passed ? 'Good' : 'Needs Review'}`)
    })
    
    return checks
  },

  // Check security headers
  checkSecurity: async () => {
    try {
      const response = await fetch(window.location.href, { method: 'HEAD' })
      const headers = {
        'Content-Security-Policy': response.headers.get('content-security-policy'),
        'X-Frame-Options': response.headers.get('x-frame-options'),
        'X-Content-Type-Options': response.headers.get('x-content-type-options'),
        'Referrer-Policy': response.headers.get('referrer-policy'),
        'Strict-Transport-Security': response.headers.get('strict-transport-security')
      }
      
      console.log('🔒 Security Headers:')
      Object.entries(headers).forEach(([name, value]) => {
        console.log(`  ${value ? '✅' : '⚠️'} ${name}: ${value || 'Not Set'}`)
      })
      
      return headers
    } catch (error) {
      console.log('❌ Could not check security headers:', error.message)
      return null
    }
  },

  // Check responsive design
  checkResponsive: () => {
    const viewports = [
      { name: 'Mobile', width: 375, height: 667 },
      { name: 'Tablet', width: 768, height: 1024 },
      { name: 'Desktop', width: 1920, height: 1080 }
    ]
    
    console.log('📱 Responsive Design Check:')
    console.log('  Current viewport:', `${window.innerWidth}x${window.innerHeight}`)
    
    // Check for responsive meta tag
    const viewportMeta = document.querySelector('meta[name="viewport"]')
    console.log(`  ${viewportMeta ? '✅' : '❌'} Viewport meta tag: ${viewportMeta ? 'Present' : 'Missing'}`)
    
    // Check for responsive classes
    const hasResponsiveClasses = document.body.className.includes('sm:') || 
                                document.body.className.includes('md:') || 
                                document.body.className.includes('lg:')
    console.log(`  ${hasResponsiveClasses ? '✅' : '⚠️'} Responsive classes: ${hasResponsiveClasses ? 'Found' : 'Not detected in body'}`)
    
    return {
      viewportMeta: !!viewportMeta,
      currentViewport: { width: window.innerWidth, height: window.innerHeight },
      hasResponsiveClasses
    }
  },

  // Check console errors
  checkConsoleErrors: () => {
    // This would need to be implemented with a custom console override
    // For now, just remind to check manually
    console.log('🐛 Console Error Check:')
    console.log('  ℹ️ Please manually check for any console errors or warnings')
    console.log('  ℹ️ Look for red error messages or yellow warnings in the console')
    
    return { manualCheckRequired: true }
  },

  // Check local storage and session storage
  checkStorage: () => {
    const localStorage = window.localStorage
    const sessionStorage = window.sessionStorage
    
    console.log('💾 Storage Check:')
    console.log(`  📦 Local Storage items: ${localStorage.length}`)
    console.log(`  📦 Session Storage items: ${sessionStorage.length}`)
    
    // Check for common items
    const commonItems = ['firebase:authUser', 'csrf_token', 'theme', 'user_preferences']
    commonItems.forEach(item => {
      const inLocal = localStorage.getItem(item) !== null
      const inSession = sessionStorage.getItem(item) !== null
      if (inLocal || inSession) {
        console.log(`  ✅ ${item}: Found in ${inLocal ? 'localStorage' : 'sessionStorage'}`)
      }
    })
    
    return {
      localStorageCount: localStorage.length,
      sessionStorageCount: sessionStorage.length
    }
  },

  // Check network requests
  checkNetworkRequests: () => {
    if (window.performance && window.performance.getEntriesByType) {
      const resources = window.performance.getEntriesByType('resource')
      const requests = {
        total: resources.length,
        scripts: resources.filter(r => r.name.includes('.js')).length,
        styles: resources.filter(r => r.name.includes('.css')).length,
        images: resources.filter(r => r.name.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)).length,
        fonts: resources.filter(r => r.name.match(/\.(woff|woff2|ttf|otf)$/)).length,
        api: resources.filter(r => r.name.includes('/api/')).length
      }
      
      console.log('🌐 Network Requests:')
      Object.entries(requests).forEach(([type, count]) => {
        console.log(`  📊 ${type}: ${count}`)
      })
      
      return requests
    }
    
    console.log('❌ Performance API not available for network check')
    return null
  }
}

// Run all verification checks
const runAllVerificationChecks = async () => {
  console.log('🚀 Running Complete Verification Suite...\n')
  
  const results = {}
  
  try {
    results.components = verificationChecks.checkComponentsLoaded()
    console.log('')
    
    results.performance = verificationChecks.checkPerformance()
    console.log('')
    
    results.accessibility = verificationChecks.checkAccessibility()
    console.log('')
    
    results.security = await verificationChecks.checkSecurity()
    console.log('')
    
    results.responsive = verificationChecks.checkResponsive()
    console.log('')
    
    results.storage = verificationChecks.checkStorage()
    console.log('')
    
    results.network = verificationChecks.checkNetworkRequests()
    console.log('')
    
    results.consoleErrors = verificationChecks.checkConsoleErrors()
    console.log('')
    
    // Generate summary
    console.log('📋 VERIFICATION SUMMARY:')
    console.log('========================')
    
    const componentsPassed = Object.values(results.components).filter(Boolean).length
    const componentsTotal = Object.keys(results.components).length
    console.log(`🔧 Components: ${componentsPassed}/${componentsTotal} loaded`)
    
    if (results.performance) {
      console.log(`⚡ Performance: ${results.performance.grade}`)
    }
    
    const accessibilityPassed = Object.values(results.accessibility).filter(Boolean).length
    const accessibilityTotal = Object.keys(results.accessibility).length
    console.log(`♿ Accessibility: ${accessibilityPassed}/${accessibilityTotal} checks passed`)
    
    console.log(`💾 Storage: ${results.storage.localStorageCount + results.storage.sessionStorageCount} items stored`)
    
    if (results.network) {
      console.log(`🌐 Network: ${results.network.total} total requests`)
    }
    
    console.log('\n✅ Verification Complete!')
    console.log('📊 Results stored in window.verificationResults')
    
    // Store results globally for further inspection
    window.verificationResults = results
    
    return results
    
  } catch (error) {
    console.error('❌ Verification failed:', error)
    return { error: error.message }
  }
}

// Auto-run verification if this script is executed
if (typeof window !== 'undefined') {
  // Make functions available globally
  window.verificationChecks = verificationChecks
  window.runAllVerificationChecks = runAllVerificationChecks
  
  console.log('🔧 Verification tools loaded!')
  console.log('📝 Run window.runAllVerificationChecks() to start complete verification')
  console.log('🔍 Or run individual checks like window.verificationChecks.checkPerformance()')
}

// Export for use in other contexts
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { verificationChecks, runAllVerificationChecks }
}
