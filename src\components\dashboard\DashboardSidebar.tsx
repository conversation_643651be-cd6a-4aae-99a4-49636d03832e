'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useAuth } from '@/components/providers/AuthProvider'
import { useAdminProfile } from '@/hooks/useAdminProfile'
import {
  HomeIcon,
  DocumentTextIcon,
  RocketLaunchIcon,
  PhotoIcon,
  Cog6ToothIcon,
  PlusIcon,
  EyeIcon,
  ChatBubbleLeftIcon,
  SparklesIcon
} from '@heroicons/react/24/outline'

const navigation = [
  { name: 'Dashboard', href: '/dashboard', icon: HomeIcon },
  { name: 'AI Generator', href: '/dashboard/ai-generator', icon: SparklesIcon },
  { name: 'Blog Posts', href: '/dashboard/posts', icon: DocumentTextIcon },
  { name: 'Projects', href: '/dashboard/projects', icon: RocketLaunchIcon },
  { name: 'Comments', href: '/dashboard/comments', icon: ChatBubbleLeftIcon },
  { name: 'Media', href: '/dashboard/media', icon: PhotoIcon },
  { name: 'Setting<PERSON>', href: '/dashboard/settings', icon: Cog6ToothIcon },
]

const quickActions = [
  { name: 'AI Generator', href: '/dashboard/ai-generator', icon: SparklesIcon },
  { name: 'AI Monitoring', href: '/dashboard/ai-generator/monitoring', icon: EyeIcon },
  { name: 'Performance Test', href: '/dashboard/performance-test', icon: EyeIcon },
  { name: 'Security Test', href: '/dashboard/security-test', icon: EyeIcon },
  { name: 'New Blog Post', href: '/dashboard/posts/new', icon: PlusIcon },
  { name: 'New Project', href: '/dashboard/projects/new', icon: PlusIcon },
  { name: 'View Site', href: '/', icon: EyeIcon, external: true },
]

export default function DashboardSidebar() {
  const pathname = usePathname()
  const { user } = useAuth()
  const { avatarSrc, displayName } = useAdminProfile()

  return (
    <div className="hidden lg:flex lg:flex-shrink-0">
      <div className="flex flex-col w-64 p-6">
        <div className="flex flex-col flex-grow dashboard-card rounded-lg shadow-lg pt-6 pb-6 overflow-y-auto">
          <div className="flex items-center flex-shrink-0 px-6">
            <img
              src={avatarSrc}
              alt={displayName}
              className="w-10 h-10 rounded-xl shadow-sm object-cover"
            />
            <div className="ml-3">
              <h2 className="text-lg font-semibold tracking-tight dashboard-text">
                Dashboard
              </h2>
              <p className="text-sm dashboard-muted">
                Content Management
              </p>
            </div>
          </div>

          <nav className="mt-8 flex-1 px-3 space-y-2">
            {navigation.map((item) => {
              const isActive = pathname === item.href
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`
                    dashboard-link group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 relative
                    ${isActive
                      ? 'bg-gray-100 dark:bg-gray-800 dashboard-text shadow-sm border border-gray-200 dark:border-gray-700'
                      : 'dashboard-text hover:bg-gray-50 dark:hover:bg-gray-900'
                    }
                  `}
                >
                  {isActive && (
                    <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-6 bg-black dark:bg-white rounded-r-full" />
                  )}
                  <item.icon
                    className={`
                      mr-3 h-5 w-5 flex-shrink-0 transition-colors dashboard-text
                    `}
                  />
                  {item.name}
                  {isActive && (
                    <div className="ml-auto w-2 h-2 bg-black dark:bg-white rounded-full" />
                  )}
                </Link>
              )
            })}

            {/* Quick Actions */}
            <div className="mt-8 pt-6 dashboard-border border-t">
              <h3 className="px-3 text-xs font-semibold dashboard-muted uppercase tracking-wider mb-3">
                Quick Actions
              </h3>
              <div className="space-y-1">
                {quickActions.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    target={item.external ? '_blank' : undefined}
                    rel={item.external ? 'noopener noreferrer' : undefined}
                    className="dashboard-link group flex items-center px-3 py-2.5 text-sm font-medium dashboard-text rounded-lg hover:bg-gray-50 dark:hover:bg-gray-900 transition-all duration-200"
                  >
                    <item.icon className="mr-3 h-4 w-4 flex-shrink-0 transition-colors" />
                    {item.name}
                    {item.external && (
                      <svg className="ml-auto h-3 w-3 transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4.25 5.5a.75.75 0 00-.75.75v8.5c0 .414.336.75.75.75h8.5a.75.75 0 00.75-.75v-4a.75.75 0 011.5 0v4A2.25 2.25 0 0112.75 17h-8.5A2.25 2.25 0 012 14.75v-8.5A2.25 2.25 0 014.25 4h5a.75.75 0 010 1.5h-5z" clipRule="evenodd" />
                        <path fillRule="evenodd" d="M6.194 12.753a.75.75 0 001.06.053L16.5 4.44v2.81a.75.75 0 001.5 0v-4.5a.75.75 0 00-.75-.75h-4.5a.75.75 0 000 1.5h2.553l-9.056 8.194a.75.75 0 00-.053 1.06z" clipRule="evenodd" />
                      </svg>
                    )}
                  </Link>
                ))}
              </div>
            </div>
          </nav>


        </div>
      </div>
    </div>
  )
}
