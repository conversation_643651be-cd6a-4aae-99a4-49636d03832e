'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { useRouter } from 'next/navigation'
import { 
  CogIcon, 
  CheckIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import {
  DashboardCard,
  DashboardCardContent,
  DashboardCardDescription,
  DashboardCardHeader,
  DashboardCardTitle
} from '@/components/dashboard/DashboardCard'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  getOrCreateAISettings, 
  saveAISettings, 
  validateAISettings,
  getAIProviderStatus,
  aiProviderManager
} from '@/lib/ai'
import { AISettings, AIProvider, AI_MODELS, DEFAULT_AI_SETTINGS } from '@/types/ai'

export default function AISettingsPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [settings, setSettings] = useState<AISettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [errors, setErrors] = useState<string[]>([])
  const [successMessage, setSuccessMessage] = useState('')
  const [providerStatus, setProviderStatus] = useState({
    openai: false,
    gemini: false,
    openrouter: false
  })

  useEffect(() => {
    if (user) {
      loadSettings()
    }
  }, [user])

  const loadSettings = async () => {
    if (!user) return

    try {
      // Try to load settings directly from client-side Firebase
      const userSettings = await getOrCreateAISettings(user.uid)
      setSettings(userSettings)
      setProviderStatus(getAIProviderStatus())
    } catch (error) {
      console.error('Error loading settings:', error)
      // If loading fails, create default settings
      try {
        const defaultSettings = {
          ...DEFAULT_AI_SETTINGS,
          user_id: user.uid,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
        setSettings(defaultSettings)
        setProviderStatus(getAIProviderStatus())
        setErrors(['Using default settings. You can customize them below.'])
      } catch (defaultError) {
        setErrors(['Failed to load AI settings. Please refresh the page.'])
      }
    } finally {
      setLoading(false)
    }
  }

  const handleProviderChange = (provider: AIProvider) => {
    if (!settings) return

    // Update provider and reset models to defaults for that provider
    const defaultModels = {
      research: AI_MODELS[provider][0]?.id || '',
      outline: AI_MODELS[provider][0]?.id || '',
      content: AI_MODELS[provider][0]?.id || '',
      metadata: AI_MODELS[provider][0]?.id || ''
    }

    setSettings({
      ...settings,
      provider,
      models: defaultModels
    })
  }

  const handleModelChange = (taskType: keyof AISettings['models'], modelId: string) => {
    if (!settings) return

    setSettings({
      ...settings,
      models: {
        ...settings.models,
        [taskType]: modelId
      }
    })
  }

  const handlePreferenceChange = (key: keyof AISettings['preferences'], value: any) => {
    if (!settings) return

    setSettings({
      ...settings,
      preferences: {
        ...settings.preferences,
        [key]: value
      }
    })
  }

  const handleSave = async () => {
    if (!settings || !user) return

    setSaving(true)
    setErrors([])
    setSuccessMessage('')

    try {
      // Validate settings
      const validationErrors = validateAISettings(settings)
      if (validationErrors.length > 0) {
        setErrors(validationErrors)
        return
      }

      // Save settings
      await saveAISettings(user.uid, settings)
      setSuccessMessage('AI settings saved successfully!')
      
      // Redirect back to main AI generator page after a delay
      setTimeout(() => {
        router.push('/dashboard/ai-generator')
      }, 2000)
    } catch (error) {
      console.error('Error saving settings:', error)
      setErrors(['Failed to save AI settings'])
    } finally {
      setSaving(false)
    }
  }

  const resetToDefaults = () => {
    if (!user) return

    setSettings({
      ...DEFAULT_AI_SETTINGS,
      id: settings?.id,
      user_id: user.uid,
      created_at: settings?.created_at || new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading AI settings...</p>
        </div>
      </div>
    )
  }

  if (!settings) {
    return (
      <div className="text-center py-12">
        <ExclamationTriangleIcon className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold mb-2">Failed to Load Settings</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-4">
          Unable to load AI settings. Please try again.
        </p>
        <Button onClick={loadSettings}>Retry</Button>
      </div>
    )
  }

  const availableProviders = Object.entries(providerStatus)
    .filter(([_, available]) => available)
    .map(([provider, _]) => provider as AIProvider)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight dashboard-text flex items-center gap-2">
            <CogIcon className="w-7 h-7 text-blue-600" />
            AI Settings
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Configure your AI providers and generation preferences
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={resetToDefaults}>
            Reset to Defaults
          </Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? 'Saving...' : 'Save Settings'}
          </Button>
        </div>
      </div>

      {/* Messages */}
      {errors.length > 0 && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-start">
            <ExclamationTriangleIcon className="w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Please fix the following errors:
              </h3>
              <ul className="mt-2 text-sm text-red-700 dark:text-red-300 list-disc list-inside">
                {errors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      )}

      {successMessage && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center">
            <CheckIcon className="w-5 h-5 text-green-500 mr-3" />
            <p className="text-sm text-green-800 dark:text-green-200">{successMessage}</p>
          </div>
        </div>
      )}

      {/* AI Provider Selection */}
      <DashboardCard>
        <DashboardCardHeader>
          <DashboardCardTitle>AI Provider</DashboardCardTitle>
          <DashboardCardDescription>
            Choose your preferred AI provider for content generation
          </DashboardCardDescription>
        </DashboardCardHeader>
        <DashboardCardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {(['openai', 'gemini', 'openrouter'] as AIProvider[]).map((provider) => {
              const isAvailable = providerStatus[provider]
              const isSelected = settings.provider === provider
              
              return (
                <button
                  key={provider}
                  onClick={() => isAvailable && handleProviderChange(provider)}
                  disabled={!isAvailable}
                  className={`
                    p-4 rounded-lg border-2 transition-all duration-200 text-left
                    ${isSelected 
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                      : isAvailable 
                        ? 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600' 
                        : 'border-gray-100 dark:border-gray-800 opacity-50 cursor-not-allowed'
                    }
                  `}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-semibold capitalize">{provider}</h3>
                    {isSelected && <CheckIcon className="w-5 h-5 text-blue-500" />}
                  </div>
                  <Badge 
                    className={
                      isAvailable 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' 
                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                    }
                  >
                    {isAvailable ? 'Available' : 'Not Configured'}
                  </Badge>
                </button>
              )
            })}
          </div>
          
          {availableProviders.length === 0 && (
            <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
              <div className="flex items-start">
                <InformationCircleIcon className="w-5 h-5 text-yellow-500 mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    No AI Providers Available
                  </h3>
                  <p className="mt-1 text-sm text-yellow-700 dark:text-yellow-300">
                    Please configure at least one AI provider in your environment variables to use the AI blog generator.
                  </p>
                </div>
              </div>
            </div>
          )}
        </DashboardCardContent>
      </DashboardCard>

      {/* Model Selection */}
      {settings.provider && providerStatus[settings.provider] && (
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle>Model Selection</DashboardCardTitle>
            <DashboardCardDescription>
              Choose specific models for different tasks to optimize cost and quality
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            <div className="space-y-6">
              {Object.entries(settings.models).map(([taskType, selectedModel]) => (
                <div key={taskType}>
                  <label className="block text-sm font-medium mb-2 capitalize">
                    {taskType} Model
                  </label>
                  <select
                    value={selectedModel}
                    onChange={(e) => handleModelChange(taskType as keyof AISettings['models'], e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    {AI_MODELS[settings.provider].map((model) => (
                      <option key={model.id} value={model.id}>
                        {model.name} - ${model.costPer1kTokens}/1k tokens
                      </option>
                    ))}
                  </select>
                  <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    {AI_MODELS[settings.provider].find(m => m.id === selectedModel)?.description}
                  </p>
                </div>
              ))}
            </div>
          </DashboardCardContent>
        </DashboardCard>
      )}

      {/* Generation Preferences */}
      <DashboardCard>
        <DashboardCardHeader>
          <DashboardCardTitle>Generation Preferences</DashboardCardTitle>
          <DashboardCardDescription>
            Configure generation parameters and limits
          </DashboardCardDescription>
        </DashboardCardHeader>
        <DashboardCardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium mb-2">
                Max Tokens per Request
              </label>
              <input
                type="number"
                min="100"
                max="100000"
                value={settings.preferences.maxTokensPerRequest}
                onChange={(e) => handlePreferenceChange('maxTokensPerRequest', parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                Maximum tokens to use per AI request (100-100,000)
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Temperature
              </label>
              <input
                type="number"
                min="0"
                max="2"
                step="0.1"
                value={settings.preferences.temperature}
                onChange={(e) => handlePreferenceChange('temperature', parseFloat(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                Controls randomness: 0 = focused, 2 = creative (0.0-2.0)
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Monthly Cost Limit (USD)
              </label>
              <input
                type="number"
                min="0"
                step="0.01"
                value={settings.preferences.costLimit}
                onChange={(e) => handlePreferenceChange('costLimit', parseFloat(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              />
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                Maximum amount to spend on AI generation per month
              </p>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="enableCaching"
                checked={settings.preferences.enableCaching}
                onChange={(e) => handlePreferenceChange('enableCaching', e.target.checked)}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
              />
              <label htmlFor="enableCaching" className="ml-2 text-sm font-medium">
                Enable Caching
              </label>
              <div className="ml-auto">
                <InformationCircleIcon className="w-4 h-4 text-gray-400" title="Cache responses to reduce costs and improve speed" />
              </div>
            </div>
          </div>
        </DashboardCardContent>
      </DashboardCard>
    </div>
  )
}
