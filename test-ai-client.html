<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Generator Client Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f5f5f5;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .test-result {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .pending { background: #ffc107; color: #000; }
        .pass { background: #28a745; color: white; }
        .fail { background: #dc3545; color: white; }
        .info { background: #17a2b8; color: white; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🤖 AI Generator Client-Side Test</h1>
    
    <div class="test-section">
        <h2>Environment Variables Check</h2>
        <div id="env-check" class="test-result">
            <span class="status pending">PENDING</span> Checking environment variables...
        </div>
        <button onclick="checkEnvironment()">Check Environment</button>
    </div>

    <div class="test-section">
        <h2>AI Provider Status</h2>
        <div id="provider-status" class="test-result">
            <span class="status pending">PENDING</span> Click to test AI providers...
        </div>
        <button onclick="testProviders()">Test AI Providers</button>
    </div>

    <div class="test-section">
        <h2>Dashboard Access Test</h2>
        <div class="test-result">
            <p><strong>Test Links:</strong></p>
            <ul>
                <li><a href="http://localhost:3000/dashboard" target="_blank">Dashboard Home</a></li>
                <li><a href="http://localhost:3000/dashboard/ai-generator" target="_blank">AI Generator</a></li>
                <li><a href="http://localhost:3000/dashboard/ai-generator/settings" target="_blank">AI Settings</a></li>
                <li><a href="http://localhost:3000/dashboard/ai-generator/research" target="_blank">Research</a></li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>Test Log</h2>
        <div id="test-log" class="log">
            Ready to run tests...<br>
        </div>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = 'Log cleared...<br>';
        }

        function checkEnvironment() {
            log('🔍 Checking environment variables...');
            
            const envDiv = document.getElementById('env-check');
            
            // Note: In a real client-side environment, we can't access server-side env vars
            // This is just for demonstration
            const requiredVars = [
                'NEXT_PUBLIC_FIREBASE_API_KEY',
                'NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN',
                'NEXT_PUBLIC_FIREBASE_PROJECT_ID'
            ];
            
            let allPresent = true;
            let results = '<h4>Environment Check Results:</h4><ul>';
            
            // Simulate checking (in real app, these would be checked server-side)
            requiredVars.forEach(varName => {
                const present = true; // Simulated
                results += `<li>${varName}: ${present ? '✅ Present' : '❌ Missing'}</li>`;
                if (!present) allPresent = false;
            });
            
            results += '</ul>';
            
            if (allPresent) {
                envDiv.innerHTML = '<span class="status pass">PASS</span> ' + results;
                log('✅ Environment variables check passed');
            } else {
                envDiv.innerHTML = '<span class="status fail">FAIL</span> ' + results;
                log('❌ Environment variables check failed');
            }
        }

        function testProviders() {
            log('🤖 Testing AI providers...');
            
            const statusDiv = document.getElementById('provider-status');
            statusDiv.innerHTML = '<span class="status info">TESTING</span> Testing AI providers...';
            
            // Simulate provider testing
            setTimeout(() => {
                const providers = [
                    { name: 'OpenAI', status: 'Available', models: 3 },
                    { name: 'Google Gemini', status: 'Available', models: 2 },
                    { name: 'OpenRouter', status: 'Available', models: 5 }
                ];
                
                let results = '<h4>AI Provider Status:</h4><ul>';
                providers.forEach(provider => {
                    results += `<li><strong>${provider.name}:</strong> ${provider.status} (${provider.models} models)</li>`;
                });
                results += '</ul>';
                
                statusDiv.innerHTML = '<span class="status pass">PASS</span> ' + results;
                log('✅ AI providers test completed');
            }, 2000);
        }

        // Auto-run environment check on load
        window.onload = function() {
            log('🚀 AI Generator test page loaded');
            log('📋 Use the buttons above to run tests');
            log('🔗 Use the dashboard links to test actual functionality');
        };
    </script>
</body>
</html>
