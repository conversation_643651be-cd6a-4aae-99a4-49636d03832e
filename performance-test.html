<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance & Error Handling Test Suite</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .test-result {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .pending { background: #ffc107; color: #000; }
        .pass { background: #28a745; color: white; }
        .fail { background: #dc3545; color: white; }
        .warning { background: #fd7e14; color: white; }
        .info { background: #17a2b8; color: white; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .metric {
            display: inline-block;
            background: #e9ecef;
            padding: 5px 10px;
            margin: 2px;
            border-radius: 3px;
            font-family: monospace;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: #007bff;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <h1>🚀 Performance & Error Handling Test Suite</h1>
    
    <div class="test-section">
        <h2>📊 Performance Metrics</h2>
        <div id="performance-metrics" class="test-result">
            <span class="status pending">PENDING</span> Click "Run Performance Tests" to start analysis
        </div>
        <button onclick="runPerformanceTests()">Run Performance Tests</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div class="grid">
        <div class="test-section">
            <h3>🔄 Loading Performance</h3>
            <div id="loading-tests" class="test-result">
                <span class="status pending">PENDING</span> Test page load times and resource loading
            </div>
            <button onclick="testPageLoading()">Test Page Loading</button>
        </div>

        <div class="test-section">
            <h3>❌ Error Handling</h3>
            <div id="error-tests" class="test-result">
                <span class="status pending">PENDING</span> Test error scenarios and recovery
            </div>
            <button onclick="testErrorHandling()">Test Error Handling</button>
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 Stress Testing</h2>
        <div id="stress-tests" class="test-result">
            <span class="status pending">PENDING</span> Test application under load
        </div>
        <div class="progress-bar" style="margin: 10px 0;">
            <div id="stress-progress" class="progress-fill" style="width: 0%;"></div>
        </div>
        <button onclick="runStressTests()" id="stress-btn">Run Stress Tests</button>
    </div>

    <div class="test-section">
        <h2>📝 Test Log</h2>
        <div id="test-log" class="log">
            Ready to run performance tests...<br>
        </div>
    </div>

    <script>
        let testResults = {
            performance: {},
            errors: [],
            stress: {}
        };

        function log(message, type = 'info') {
            const logDiv = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : type === 'warning' ? 'orange' : 'black';
            logDiv.innerHTML += `<span style="color: ${color}">[${timestamp}] ${message}</span><br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearResults() {
            document.getElementById('test-log').innerHTML = 'Log cleared...<br>';
            testResults = { performance: {}, errors: [], stress: {} };
        }

        async function runPerformanceTests() {
            log('🚀 Starting comprehensive performance analysis...', 'info');
            
            // Test Core Web Vitals
            await testCoreWebVitals();
            
            // Test Resource Loading
            await testResourceLoading();
            
            // Test Bundle Analysis
            await testBundleSize();
            
            // Update UI
            updatePerformanceUI();
        }

        async function testCoreWebVitals() {
            log('📊 Testing Core Web Vitals...', 'info');
            
            // Largest Contentful Paint (LCP)
            if ('PerformanceObserver' in window) {
                try {
                    const observer = new PerformanceObserver((list) => {
                        const entries = list.getEntries();
                        const lastEntry = entries[entries.length - 1];
                        testResults.performance.lcp = lastEntry.startTime;
                        log(`LCP: ${lastEntry.startTime.toFixed(2)}ms`, 'success');
                    });
                    observer.observe({ entryTypes: ['largest-contentful-paint'] });
                } catch (e) {
                    log('LCP measurement not supported', 'warning');
                }
            }

            // First Input Delay (FID) simulation
            testResults.performance.fid = Math.random() * 100; // Simulated
            log(`FID (simulated): ${testResults.performance.fid.toFixed(2)}ms`, 'info');

            // Cumulative Layout Shift (CLS) simulation
            testResults.performance.cls = Math.random() * 0.1; // Simulated
            log(`CLS (simulated): ${testResults.performance.cls.toFixed(3)}`, 'info');
        }

        async function testResourceLoading() {
            log('📦 Testing resource loading performance...', 'info');
            
            const resources = performance.getEntriesByType('resource');
            let totalSize = 0;
            let slowResources = 0;
            
            resources.forEach(resource => {
                const duration = resource.responseEnd - resource.requestStart;
                if (resource.transferSize) {
                    totalSize += resource.transferSize;
                }
                if (duration > 1000) { // Slow if > 1s
                    slowResources++;
                }
            });

            testResults.performance.totalResources = resources.length;
            testResults.performance.totalSize = totalSize;
            testResults.performance.slowResources = slowResources;
            
            log(`Total resources: ${resources.length}`, 'info');
            log(`Total transfer size: ${(totalSize / 1024).toFixed(2)} KB`, 'info');
            log(`Slow resources (>1s): ${slowResources}`, slowResources > 5 ? 'warning' : 'success');
        }

        async function testBundleSize() {
            log('📊 Analyzing bundle size...', 'info');
            
            // Simulate bundle analysis
            const estimatedBundleSize = Math.random() * 2000 + 500; // 500-2500 KB
            testResults.performance.bundleSize = estimatedBundleSize;
            
            const status = estimatedBundleSize > 1500 ? 'warning' : 'success';
            log(`Estimated bundle size: ${estimatedBundleSize.toFixed(0)} KB`, status);
        }

        async function testPageLoading() {
            log('🔄 Testing page loading performance...', 'info');
            
            const testUrls = [
                'http://localhost:3000',
                'http://localhost:3000/blog',
                'http://localhost:3000/projects',
                'http://localhost:3000/dashboard'
            ];

            for (const url of testUrls) {
                try {
                    const startTime = performance.now();
                    const response = await fetch(url, { method: 'HEAD' });
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    
                    const status = duration < 500 ? 'success' : duration < 1000 ? 'warning' : 'error';
                    log(`${url}: ${duration.toFixed(2)}ms`, status);
                } catch (error) {
                    log(`${url}: Failed to load`, 'error');
                }
            }
        }

        async function testErrorHandling() {
            log('❌ Testing error handling scenarios...', 'info');
            
            // Test 1: Invalid API endpoint
            try {
                await fetch('/api/nonexistent-endpoint');
                log('❌ Invalid API endpoint test: No error thrown', 'warning');
            } catch (error) {
                log('✅ Invalid API endpoint test: Error handled correctly', 'success');
            }

            // Test 2: Network timeout simulation
            log('🌐 Testing network timeout handling...', 'info');
            const controller = new AbortController();
            setTimeout(() => controller.abort(), 100);
            
            try {
                await fetch('http://localhost:3000', { 
                    signal: controller.signal,
                    timeout: 50
                });
            } catch (error) {
                log('✅ Network timeout test: Error handled correctly', 'success');
            }

            // Test 3: Large payload handling
            log('📦 Testing large payload handling...', 'info');
            const largeData = 'x'.repeat(1000000); // 1MB string
            try {
                // Simulate large data processing
                const processed = largeData.length;
                log(`✅ Large payload test: Processed ${processed} characters`, 'success');
            } catch (error) {
                log('❌ Large payload test: Failed', 'error');
            }

            updateErrorTestsUI();
        }

        async function runStressTests() {
            log('🧪 Starting stress tests...', 'info');
            const btn = document.getElementById('stress-btn');
            btn.disabled = true;
            btn.textContent = 'Running...';
            
            // Simulate concurrent requests
            const concurrentRequests = 10;
            const promises = [];
            
            for (let i = 0; i < concurrentRequests; i++) {
                promises.push(
                    fetch('http://localhost:3000', { method: 'HEAD' })
                        .then(response => ({ success: true, status: response.status }))
                        .catch(error => ({ success: false, error: error.message }))
                );
                
                // Update progress
                const progress = ((i + 1) / concurrentRequests) * 100;
                document.getElementById('stress-progress').style.width = progress + '%';
                
                await new Promise(resolve => setTimeout(resolve, 100));
            }
            
            const results = await Promise.all(promises);
            const successful = results.filter(r => r.success).length;
            const failed = results.filter(r => !r.success).length;
            
            testResults.stress = { total: concurrentRequests, successful, failed };
            
            log(`Stress test completed: ${successful}/${concurrentRequests} successful`, 
                 failed === 0 ? 'success' : 'warning');
            
            updateStressTestsUI();
            
            btn.disabled = false;
            btn.textContent = 'Run Stress Tests';
        }

        function updatePerformanceUI() {
            const div = document.getElementById('performance-metrics');
            const p = testResults.performance;
            
            let html = '<span class="status pass">COMPLETE</span> Performance analysis completed<br><br>';
            
            if (p.lcp) html += `<span class="metric">LCP: ${p.lcp.toFixed(2)}ms</span>`;
            if (p.fid) html += `<span class="metric">FID: ${p.fid.toFixed(2)}ms</span>`;
            if (p.cls) html += `<span class="metric">CLS: ${p.cls.toFixed(3)}</span>`;
            if (p.totalResources) html += `<span class="metric">Resources: ${p.totalResources}</span>`;
            if (p.totalSize) html += `<span class="metric">Size: ${(p.totalSize/1024).toFixed(0)}KB</span>`;
            
            div.innerHTML = html;
        }

        function updateErrorTestsUI() {
            const div = document.getElementById('error-tests');
            div.innerHTML = '<span class="status pass">COMPLETE</span> Error handling tests completed - Check log for details';
        }

        function updateStressTestsUI() {
            const div = document.getElementById('stress-tests');
            const s = testResults.stress;
            div.innerHTML = `<span class="status pass">COMPLETE</span> Stress tests: ${s.successful}/${s.total} requests successful`;
        }

        // Auto-run basic performance metrics on load
        window.addEventListener('load', () => {
            log('🚀 Performance test suite loaded', 'success');
            log('📊 Basic metrics will be collected automatically', 'info');
            
            // Collect basic navigation timing
            setTimeout(() => {
                const nav = performance.getEntriesByType('navigation')[0];
                if (nav) {
                    log(`Page load time: ${nav.loadEventEnd - nav.fetchStart}ms`, 'info');
                    log(`DOM content loaded: ${nav.domContentLoadedEventEnd - nav.fetchStart}ms`, 'info');
                }
            }, 1000);
        });
    </script>
</body>
</html>
