'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { useRouter } from 'next/navigation'
import { 
  MagnifyingGlassIcon, 
  PlusIcon,
  XMarkIcon,
  InformationCircleIcon,
  SparklesIcon
} from '@heroicons/react/24/outline'
import {
  DashboardCard,
  DashboardCardContent,
  DashboardCardDescription,
  DashboardCardHeader,
  DashboardCardTitle
} from '@/components/dashboard/DashboardCard'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  getOrCreateAISettings,
  conductKeywordResearch,
  aiBlogGenerator
} from '@/lib/ai'
import { AISettings, ResearchResult, GenerationProgress, DEFAULT_AI_SETTINGS } from '@/types/ai'

export default function ResearchPage() {
  const { user } = useAuth()
  const router = useRouter()
  const [keywords, setKeywords] = useState<string[]>([])
  const [keywordInput, setKeywordInput] = useState('')
  const [settings, setSettings] = useState<AISettings | null>(null)
  const [researching, setResearching] = useState(false)
  const [researchResult, setResearchResult] = useState<ResearchResult | null>(null)
  const [progress, setProgress] = useState<GenerationProgress | null>(null)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(true)

  // Research options
  const [researchOptions, setResearchOptions] = useState({
    maxSources: 10,
    includeAcademic: false,
    includeNews: true,
    includeBlog: true,
    targetAudience: 'intermediate developers',
    contentType: 'guide' as 'tutorial' | 'guide' | 'analysis' | 'opinion' | 'news'
  })

  useEffect(() => {
    if (user) {
      loadSettings()
    }
  }, [user])

  const loadSettings = async () => {
    if (!user) return

    try {
      const userSettings = await getOrCreateAISettings(user.uid)
      setSettings(userSettings)
    } catch (error) {
      console.error('Error loading settings:', error)
      // Create default settings if loading fails
      const defaultSettings = {
        ...DEFAULT_AI_SETTINGS,
        user_id: user.uid,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
      setSettings(defaultSettings)
      setError('Using default AI settings. You can customize them in AI Settings.')
    } finally {
      setLoading(false)
    }
  }

  const addKeyword = () => {
    const trimmed = keywordInput.trim()
    if (trimmed && !keywords.includes(trimmed) && keywords.length < 10) {
      setKeywords([...keywords, trimmed])
      setKeywordInput('')
    }
  }

  const removeKeyword = (index: number) => {
    setKeywords(keywords.filter((_, i) => i !== index))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addKeyword()
    }
  }

  const startResearch = async () => {
    if (!user || !settings || keywords.length === 0) return

    setResearching(true)
    setError('')
    setResearchResult(null)
    setProgress(null)

    try {
      // Validate requirements
      const validation = aiBlogGenerator.validateRequirements(keywords, user.uid)
      if (!validation.isValid) {
        setError(validation.issues.join(', '))
        return
      }

      // Start research
      const result = await conductKeywordResearch(keywords, user.uid, researchOptions)
      setResearchResult(result)

      // Store research data in session storage for next step
      sessionStorage.setItem('ai-research-result', JSON.stringify(result))
      sessionStorage.setItem('ai-keywords', JSON.stringify(keywords))
      sessionStorage.setItem('ai-research-options', JSON.stringify(researchOptions))

    } catch (error) {
      console.error('Research failed:', error)
      setError(`Research failed: ${error}`)
    } finally {
      setResearching(false)
    }
  }

  const proceedToOutline = () => {
    router.push('/dashboard/ai-generator/outline')
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading research interface...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight dashboard-text flex items-center gap-2">
            <MagnifyingGlassIcon className="w-7 h-7 text-blue-600" />
            Keyword Research
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Enter keywords to start AI-powered research for your blog post
          </p>
        </div>
        <Button 
          variant="outline" 
          onClick={() => router.push('/dashboard/ai-generator')}
        >
          ← Back to AI Generator
        </Button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-start">
            <XMarkIcon className="w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Research Error
              </h3>
              <p className="mt-1 text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Keyword Input */}
      <DashboardCard>
        <DashboardCardHeader>
          <DashboardCardTitle>Keywords</DashboardCardTitle>
          <DashboardCardDescription>
            Add keywords that describe your blog topic (maximum 10)
          </DashboardCardDescription>
        </DashboardCardHeader>
        <DashboardCardContent>
          <div className="space-y-4">
            {/* Keyword Input */}
            <div className="flex gap-2">
              <input
                type="text"
                value={keywordInput}
                onChange={(e) => setKeywordInput(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Enter a keyword..."
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                disabled={keywords.length >= 10}
              />
              <Button 
                onClick={addKeyword}
                disabled={!keywordInput.trim() || keywords.length >= 10}
              >
                <PlusIcon className="w-4 h-4" />
              </Button>
            </div>

            {/* Keywords Display */}
            {keywords.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {keywords.map((keyword, index) => (
                  <Badge 
                    key={index}
                    variant="outline"
                    className="flex items-center gap-1 px-3 py-1"
                  >
                    {keyword}
                    <button
                      onClick={() => removeKeyword(index)}
                      className="ml-1 hover:text-red-500"
                    >
                      <XMarkIcon className="w-3 h-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            )}

            {keywords.length === 0 && (
              <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                <MagnifyingGlassIcon className="w-12 h-12 mx-auto mb-3 opacity-50" />
                <p>Add keywords to start your research</p>
              </div>
            )}
          </div>
        </DashboardCardContent>
      </DashboardCard>

      {/* Research Options */}
      <DashboardCard>
        <DashboardCardHeader>
          <DashboardCardTitle>Research Options</DashboardCardTitle>
          <DashboardCardDescription>
            Configure research parameters and content preferences
          </DashboardCardDescription>
        </DashboardCardHeader>
        <DashboardCardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium mb-2">
                Maximum Sources
              </label>
              <select
                value={researchOptions.maxSources}
                onChange={(e) => setResearchOptions({
                  ...researchOptions,
                  maxSources: parseInt(e.target.value)
                })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value={5}>5 sources</option>
                <option value={10}>10 sources</option>
                <option value={15}>15 sources</option>
                <option value={20}>20 sources</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Target Audience
              </label>
              <select
                value={researchOptions.targetAudience}
                onChange={(e) => setResearchOptions({
                  ...researchOptions,
                  targetAudience: e.target.value
                })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value="beginner developers">Beginner Developers</option>
                <option value="intermediate developers">Intermediate Developers</option>
                <option value="advanced developers">Advanced Developers</option>
                <option value="general audience">General Audience</option>
                <option value="business professionals">Business Professionals</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">
                Content Type
              </label>
              <select
                value={researchOptions.contentType}
                onChange={(e) => setResearchOptions({
                  ...researchOptions,
                  contentType: e.target.value as any
                })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
              >
                <option value="guide">Guide</option>
                <option value="tutorial">Tutorial</option>
                <option value="analysis">Analysis</option>
                <option value="opinion">Opinion</option>
                <option value="news">News</option>
              </select>
            </div>

            <div className="space-y-3">
              <label className="block text-sm font-medium">
                Source Types
              </label>
              <div className="space-y-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={researchOptions.includeNews}
                    onChange={(e) => setResearchOptions({
                      ...researchOptions,
                      includeNews: e.target.checked
                    })}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm">News Articles</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={researchOptions.includeBlog}
                    onChange={(e) => setResearchOptions({
                      ...researchOptions,
                      includeBlog: e.target.checked
                    })}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm">Blog Posts</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={researchOptions.includeAcademic}
                    onChange={(e) => setResearchOptions({
                      ...researchOptions,
                      includeAcademic: e.target.checked
                    })}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm">Academic Papers</span>
                </label>
              </div>
            </div>
          </div>
        </DashboardCardContent>
      </DashboardCard>

      {/* Research Results */}
      {researchResult && (
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle className="flex items-center gap-2">
              <SparklesIcon className="w-5 h-5 text-green-500" />
              Research Complete
            </DashboardCardTitle>
            <DashboardCardDescription>
              Found {researchResult.sources.length} sources • Cost: ${researchResult.cost.toFixed(4)}
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            <div className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Summary</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {researchResult.summary}
                </p>
              </div>

              <div>
                <h3 className="font-medium mb-2">Key Points</h3>
                <ul className="text-sm text-gray-600 dark:text-gray-400 list-disc list-inside space-y-1">
                  {researchResult.keyPoints.map((point, index) => (
                    <li key={index}>{point}</li>
                  ))}
                </ul>
              </div>

              <div>
                <h3 className="font-medium mb-2">Related Topics</h3>
                <div className="flex flex-wrap gap-2">
                  {researchResult.relatedTopics.map((topic, index) => (
                    <Badge key={index} variant="outline">
                      {topic}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="pt-4 border-t">
                <Button onClick={proceedToOutline} className="w-full">
                  Proceed to Outline Generation →
                </Button>
              </div>
            </div>
          </DashboardCardContent>
        </DashboardCard>
      )}

      {/* Start Research Button */}
      {!researchResult && (
        <div className="flex justify-center">
          <Button 
            onClick={startResearch}
            disabled={keywords.length === 0 || researching || !settings}
            size="lg"
            className="px-8"
          >
            {researching ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Researching...
              </>
            ) : (
              <>
                <MagnifyingGlassIcon className="w-5 h-5 mr-2" />
                Start Research
              </>
            )}
          </Button>
        </div>
      )}
    </div>
  )
}
