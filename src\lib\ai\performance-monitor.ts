// AI Performance Monitoring System
'use client'

export interface PerformanceMetric {
  id: string
  userId: string
  operation: string
  provider: string
  model?: string
  startTime: number
  endTime: number
  duration: number
  tokensUsed: number
  cost: number
  success: boolean
  errorCode?: string
  metadata?: Record<string, any>
}

export interface UsageStats {
  totalOperations: number
  totalTokens: number
  totalCost: number
  averageDuration: number
  successRate: number
  operationsByType: Record<string, number>
  operationsByProvider: Record<string, number>
  costByProvider: Record<string, number>
  errorsByCode: Record<string, number>
}

export interface PerformanceAlert {
  id: string
  type: 'cost_limit' | 'high_latency' | 'error_rate' | 'quota_exceeded'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  userId: string
  timestamp: number
  resolved: boolean
}

// Performance Monitor Class
export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: Map<string, PerformanceMetric[]> = new Map()
  private alerts: PerformanceAlert[] = []
  private activeOperations: Map<string, { startTime: number; metadata: any }> = new Map()

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  // Start tracking an operation
  startOperation(
    operationId: string,
    userId: string,
    operation: string,
    provider: string,
    model?: string,
    metadata?: Record<string, any>
  ): void {
    this.activeOperations.set(operationId, {
      startTime: Date.now(),
      metadata: {
        userId,
        operation,
        provider,
        model,
        ...metadata
      }
    })
  }

  // End tracking an operation
  endOperation(
    operationId: string,
    success: boolean,
    tokensUsed: number = 0,
    cost: number = 0,
    errorCode?: string
  ): PerformanceMetric | null {
    const activeOp = this.activeOperations.get(operationId)
    if (!activeOp) {
      console.warn(`No active operation found for ID: ${operationId}`)
      return null
    }

    const endTime = Date.now()
    const duration = endTime - activeOp.startTime

    const metric: PerformanceMetric = {
      id: operationId,
      userId: activeOp.metadata.userId,
      operation: activeOp.metadata.operation,
      provider: activeOp.metadata.provider,
      model: activeOp.metadata.model,
      startTime: activeOp.startTime,
      endTime,
      duration,
      tokensUsed,
      cost,
      success,
      errorCode,
      metadata: activeOp.metadata
    }

    // Store metric
    this.storeMetric(metric)

    // Check for alerts
    this.checkAlerts(metric)

    // Clean up active operation
    this.activeOperations.delete(operationId)

    return metric
  }

  // Store metric in memory (in production, use database)
  private storeMetric(metric: PerformanceMetric): void {
    const userId = metric.userId
    if (!this.metrics.has(userId)) {
      this.metrics.set(userId, [])
    }

    const userMetrics = this.metrics.get(userId)!
    userMetrics.push(metric)

    // Keep only last 1000 metrics per user
    if (userMetrics.length > 1000) {
      userMetrics.splice(0, userMetrics.length - 1000)
    }
  }

  // Get usage statistics for a user
  getUserStats(userId: string, timeRange?: { start: number; end: number }): UsageStats {
    const userMetrics = this.metrics.get(userId) || []
    
    let filteredMetrics = userMetrics
    if (timeRange) {
      filteredMetrics = userMetrics.filter(
        m => m.startTime >= timeRange.start && m.startTime <= timeRange.end
      )
    }

    const totalOperations = filteredMetrics.length
    const successfulOps = filteredMetrics.filter(m => m.success)
    const totalTokens = filteredMetrics.reduce((sum, m) => sum + m.tokensUsed, 0)
    const totalCost = filteredMetrics.reduce((sum, m) => sum + m.cost, 0)
    const totalDuration = filteredMetrics.reduce((sum, m) => sum + m.duration, 0)

    const operationsByType: Record<string, number> = {}
    const operationsByProvider: Record<string, number> = {}
    const costByProvider: Record<string, number> = {}
    const errorsByCode: Record<string, number> = {}

    filteredMetrics.forEach(metric => {
      // Operations by type
      operationsByType[metric.operation] = (operationsByType[metric.operation] || 0) + 1
      
      // Operations by provider
      operationsByProvider[metric.provider] = (operationsByProvider[metric.provider] || 0) + 1
      
      // Cost by provider
      costByProvider[metric.provider] = (costByProvider[metric.provider] || 0) + metric.cost
      
      // Errors by code
      if (!metric.success && metric.errorCode) {
        errorsByCode[metric.errorCode] = (errorsByCode[metric.errorCode] || 0) + 1
      }
    })

    return {
      totalOperations,
      totalTokens,
      totalCost,
      averageDuration: totalOperations > 0 ? totalDuration / totalOperations : 0,
      successRate: totalOperations > 0 ? successfulOps.length / totalOperations : 0,
      operationsByType,
      operationsByProvider,
      costByProvider,
      errorsByCode
    }
  }

  // Get system-wide statistics
  getSystemStats(timeRange?: { start: number; end: number }): UsageStats {
    const allMetrics: PerformanceMetric[] = []
    
    for (const userMetrics of this.metrics.values()) {
      allMetrics.push(...userMetrics)
    }

    let filteredMetrics = allMetrics
    if (timeRange) {
      filteredMetrics = allMetrics.filter(
        m => m.startTime >= timeRange.start && m.startTime <= timeRange.end
      )
    }

    const totalOperations = filteredMetrics.length
    const successfulOps = filteredMetrics.filter(m => m.success)
    const totalTokens = filteredMetrics.reduce((sum, m) => sum + m.tokensUsed, 0)
    const totalCost = filteredMetrics.reduce((sum, m) => sum + m.cost, 0)
    const totalDuration = filteredMetrics.reduce((sum, m) => sum + m.duration, 0)

    const operationsByType: Record<string, number> = {}
    const operationsByProvider: Record<string, number> = {}
    const costByProvider: Record<string, number> = {}
    const errorsByCode: Record<string, number> = {}

    filteredMetrics.forEach(metric => {
      operationsByType[metric.operation] = (operationsByType[metric.operation] || 0) + 1
      operationsByProvider[metric.provider] = (operationsByProvider[metric.provider] || 0) + 1
      costByProvider[metric.provider] = (costByProvider[metric.provider] || 0) + metric.cost
      
      if (!metric.success && metric.errorCode) {
        errorsByCode[metric.errorCode] = (errorsByCode[metric.errorCode] || 0) + 1
      }
    })

    return {
      totalOperations,
      totalTokens,
      totalCost,
      averageDuration: totalOperations > 0 ? totalDuration / totalOperations : 0,
      successRate: totalOperations > 0 ? successfulOps.length / totalOperations : 0,
      operationsByType,
      operationsByProvider,
      costByProvider,
      errorsByCode
    }
  }

  // Check for performance alerts
  private checkAlerts(metric: PerformanceMetric): void {
    const userId = metric.userId
    const userStats = this.getUserStats(userId, {
      start: Date.now() - 24 * 60 * 60 * 1000, // Last 24 hours
      end: Date.now()
    })

    // Cost limit alert
    if (userStats.totalCost > 50) { // $50 threshold
      this.createAlert({
        type: 'cost_limit',
        severity: userStats.totalCost > 100 ? 'critical' : 'high',
        message: `High AI usage cost: $${userStats.totalCost.toFixed(2)} in the last 24 hours`,
        userId
      })
    }

    // High latency alert
    if (metric.duration > 60000) { // 60 seconds
      this.createAlert({
        type: 'high_latency',
        severity: metric.duration > 120000 ? 'high' : 'medium',
        message: `High latency detected: ${metric.operation} took ${(metric.duration / 1000).toFixed(1)}s`,
        userId
      })
    }

    // Error rate alert
    if (userStats.successRate < 0.8 && userStats.totalOperations >= 5) {
      this.createAlert({
        type: 'error_rate',
        severity: userStats.successRate < 0.5 ? 'critical' : 'high',
        message: `High error rate: ${((1 - userStats.successRate) * 100).toFixed(1)}% failure rate`,
        userId
      })
    }
  }

  // Create performance alert
  private createAlert(alertData: {
    type: PerformanceAlert['type']
    severity: PerformanceAlert['severity']
    message: string
    userId: string
  }): void {
    const alert: PerformanceAlert = {
      id: `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      ...alertData,
      timestamp: Date.now(),
      resolved: false
    }

    this.alerts.push(alert)

    // Keep only last 100 alerts
    if (this.alerts.length > 100) {
      this.alerts.splice(0, this.alerts.length - 100)
    }

    // Log critical alerts
    if (alert.severity === 'critical') {
      console.error('Critical AI Performance Alert:', alert)
    }
  }

  // Get alerts for a user
  getUserAlerts(userId: string, unreadOnly: boolean = false): PerformanceAlert[] {
    return this.alerts
      .filter(alert => alert.userId === userId)
      .filter(alert => !unreadOnly || !alert.resolved)
      .sort((a, b) => b.timestamp - a.timestamp)
  }

  // Resolve alert
  resolveAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId)
    if (alert) {
      alert.resolved = true
      return true
    }
    return false
  }

  // Get performance insights
  getPerformanceInsights(userId: string): {
    recommendations: string[]
    trends: {
      costTrend: 'increasing' | 'decreasing' | 'stable'
      latencyTrend: 'improving' | 'degrading' | 'stable'
      errorTrend: 'improving' | 'degrading' | 'stable'
    }
    topIssues: string[]
  } {
    const stats = this.getUserStats(userId)
    const recommendations: string[] = []
    const topIssues: string[] = []

    // Cost recommendations
    if (stats.totalCost > 20) {
      recommendations.push('Consider using more cost-effective models for simple tasks')
      recommendations.push('Enable caching to reduce duplicate API calls')
    }

    // Performance recommendations
    if (stats.averageDuration > 30000) {
      recommendations.push('Consider breaking large content into smaller batches')
      topIssues.push('High average response time')
    }

    // Error rate recommendations
    if (stats.successRate < 0.9) {
      recommendations.push('Review error patterns and implement better retry logic')
      topIssues.push('High error rate detected')
    }

    // Provider recommendations
    const providerCosts = Object.entries(stats.costByProvider)
    if (providerCosts.length > 1) {
      const mostExpensive = providerCosts.reduce((a, b) => a[1] > b[1] ? a : b)
      recommendations.push(`Consider optimizing usage of ${mostExpensive[0]} (highest cost provider)`)
    }

    return {
      recommendations,
      trends: {
        costTrend: 'stable', // Would need historical data for real trends
        latencyTrend: 'stable',
        errorTrend: 'stable'
      },
      topIssues
    }
  }

  // Export metrics for analysis
  exportMetrics(userId: string, format: 'json' | 'csv' = 'json'): string {
    const userMetrics = this.metrics.get(userId) || []
    
    if (format === 'csv') {
      const headers = ['timestamp', 'operation', 'provider', 'model', 'duration', 'tokens', 'cost', 'success', 'error']
      const rows = userMetrics.map(m => [
        new Date(m.startTime).toISOString(),
        m.operation,
        m.provider,
        m.model || '',
        m.duration,
        m.tokensUsed,
        m.cost,
        m.success,
        m.errorCode || ''
      ])
      
      return [headers, ...rows].map(row => row.join(',')).join('\n')
    }
    
    return JSON.stringify(userMetrics, null, 2)
  }
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance()

// Utility functions
export const trackOperation = (
  operationId: string,
  userId: string,
  operation: string,
  provider: string,
  model?: string,
  metadata?: Record<string, any>
) => {
  performanceMonitor.startOperation(operationId, userId, operation, provider, model, metadata)
}

export const completeOperation = (
  operationId: string,
  success: boolean,
  tokensUsed?: number,
  cost?: number,
  errorCode?: string
) => {
  return performanceMonitor.endOperation(operationId, success, tokensUsed, cost, errorCode)
}

export const getUserPerformanceStats = (userId: string, timeRange?: { start: number; end: number }) => {
  return performanceMonitor.getUserStats(userId, timeRange)
}

export const getSystemPerformanceStats = (timeRange?: { start: number; end: number }) => {
  return performanceMonitor.getSystemStats(timeRange)
}

export const getUserAlerts = (userId: string, unreadOnly?: boolean) => {
  return performanceMonitor.getUserAlerts(userId, unreadOnly)
}

export const getPerformanceInsights = (userId: string) => {
  return performanceMonitor.getPerformanceInsights(userId)
}
