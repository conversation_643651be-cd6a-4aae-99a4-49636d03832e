// AI Provider Management System
'use client'

import OpenAI from 'openai'
import { GoogleGenerativeAI } from '@google/generative-ai'
import {
  AIProvider,
  AIRequest,
  AIResponse,
  AIProviderConfig,
  AI_MODELS
} from '@/types/ai'
import { withRetry, handleAIError, providerCircuitBreaker } from './error-handler'

// Provider configurations
const getProviderConfig = (): Record<AIProvider, AIProviderConfig> => {
  return {
    openai: {
      provider: 'openai',
      apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY || '',
      models: AI_MODELS.openai
    },
    gemini: {
      provider: 'gemini',
      apiKey: process.env.NEXT_PUBLIC_GOOGLE_AI_API_KEY || '',
      models: AI_MODELS.gemini
    },
    openrouter: {
      provider: 'openrouter',
      apiKey: process.env.NEXT_PUBLIC_OPENROUTER_API_KEY || '',
      baseUrl: 'https://openrouter.ai/api/v1',
      models: AI_MODELS.openrouter
    }
  }
}

// Initialize AI clients
class AIProviderManager {
  private openaiClient: OpenAI | null = null
  private geminiClient: GoogleGenerativeAI | null = null
  private openrouterClient: OpenAI | null = null
  private configs: Record<AIProvider, AIProviderConfig>

  constructor() {
    this.configs = getProviderConfig()
    this.initializeClients()
  }

  private initializeClients() {
    // Initialize OpenAI
    if (this.configs.openai.apiKey) {
      this.openaiClient = new OpenAI({
        apiKey: this.configs.openai.apiKey,
        dangerouslyAllowBrowser: true
      })
    }

    // Initialize Gemini
    if (this.configs.gemini.apiKey) {
      this.geminiClient = new GoogleGenerativeAI(this.configs.gemini.apiKey)
    }

    // Initialize OpenRouter
    if (this.configs.openrouter.apiKey) {
      this.openrouterClient = new OpenAI({
        apiKey: this.configs.openrouter.apiKey,
        baseURL: this.configs.openrouter.baseUrl,
        dangerouslyAllowBrowser: true
      })
    }
  }

  // Get available models for a provider
  getModelsForProvider(provider: AIProvider) {
    return this.configs[provider]?.models || []
  }

  // Get model details
  getModelDetails(provider: AIProvider, modelId: string) {
    const models = this.getModelsForProvider(provider)
    return models.find(model => model.id === modelId)
  }

  // Calculate cost for tokens
  calculateCost(provider: AIProvider, modelId: string, tokens: number): number {
    const model = this.getModelDetails(provider, modelId)
    if (!model) return 0
    return (tokens / 1000) * model.costPer1kTokens
  }

  // Main method to generate AI response
  async generateResponse(request: AIRequest): Promise<AIResponse> {
    const startTime = Date.now()

    // Check circuit breaker
    if (!providerCircuitBreaker.isProviderHealthy(request.provider)) {
      throw handleAIError(
        request.provider,
        new Error(`Provider ${request.provider} is temporarily unavailable due to repeated failures`),
        { operation: 'generateResponse', provider: request.provider, userId: 'unknown' }
      )
    }

    try {
      const result = await withRetry(
        async () => {
          let response: string
          let tokensUsed: number

          switch (request.provider) {
            case 'openai':
              ({ response, tokensUsed } = await this.callOpenAI(request))
              break
            case 'gemini':
              ({ response, tokensUsed } = await this.callGemini(request))
              break
            case 'openrouter':
              ({ response, tokensUsed } = await this.callOpenRouter(request))
              break
            default:
              throw new Error(`Unsupported provider: ${request.provider}`)
          }

          return { response, tokensUsed }
        },
        {
          operation: 'generateResponse',
          provider: request.provider,
          model: request.model,
          userId: 'unknown'
        },
        {
          maxRetries: 3,
          baseDelay: 1000,
          retryableErrors: ['RATE_LIMIT_EXCEEDED', 'TIMEOUT', 'NETWORK_ERROR', 'INTERNAL_ERROR']
        }
      )

      const cost = this.calculateCost(request.provider, request.model, result.tokensUsed)

      // Record success for circuit breaker
      providerCircuitBreaker.recordSuccess(request.provider)

      return {
        content: result.response,
        tokensUsed: result.tokensUsed,
        cost,
        provider: request.provider,
        model: request.model,
        cached: false
      }
    } catch (error) {
      // Record failure for circuit breaker
      providerCircuitBreaker.recordFailure(request.provider)

      // Convert to AIError if not already
      const aiError = handleAIError(request.provider, error as Error, {
        operation: 'generateResponse',
        provider: request.provider,
        model: request.model,
        userId: 'unknown'
      })

      throw aiError
    }
  }

  private async callOpenAI(request: AIRequest): Promise<{ response: string; tokensUsed: number }> {
    if (!this.openaiClient) {
      throw new Error('OpenAI client not initialized')
    }

    const messages: any[] = []
    
    if (request.systemPrompt) {
      messages.push({ role: 'system', content: request.systemPrompt })
    }
    
    messages.push({ role: 'user', content: request.prompt })

    const completion = await this.openaiClient.chat.completions.create({
      model: request.model,
      messages,
      max_tokens: request.maxTokens || 4000,
      temperature: request.temperature || 0.7,
    })

    const response = completion.choices[0]?.message?.content || ''
    const tokensUsed = completion.usage?.total_tokens || 0

    return { response, tokensUsed }
  }

  private async callGemini(request: AIRequest): Promise<{ response: string; tokensUsed: number }> {
    if (!this.geminiClient) {
      throw new Error('Gemini client not initialized')
    }

    const model = this.geminiClient.getGenerativeModel({ 
      model: request.model,
      generationConfig: {
        maxOutputTokens: request.maxTokens || 4000,
        temperature: request.temperature || 0.7,
      }
    })

    let prompt = request.prompt
    if (request.systemPrompt) {
      prompt = `${request.systemPrompt}\n\n${request.prompt}`
    }

    const result = await model.generateContent(prompt)
    const response = result.response.text()
    
    // Estimate tokens (Gemini doesn't provide exact count in free tier)
    const tokensUsed = Math.ceil(response.length / 4)

    return { response, tokensUsed }
  }

  private async callOpenRouter(request: AIRequest): Promise<{ response: string; tokensUsed: number }> {
    if (!this.openrouterClient) {
      throw new Error('OpenRouter client not initialized')
    }

    const messages: any[] = []
    
    if (request.systemPrompt) {
      messages.push({ role: 'system', content: request.systemPrompt })
    }
    
    messages.push({ role: 'user', content: request.prompt })

    const completion = await this.openrouterClient.chat.completions.create({
      model: request.model,
      messages,
      max_tokens: request.maxTokens || 4000,
      temperature: request.temperature || 0.7,
    })

    const response = completion.choices[0]?.message?.content || ''
    const tokensUsed = completion.usage?.total_tokens || 0

    return { response, tokensUsed }
  }

  // Check if provider is available
  isProviderAvailable(provider: AIProvider): boolean {
    switch (provider) {
      case 'openai':
        return !!this.openaiClient
      case 'gemini':
        return !!this.geminiClient
      case 'openrouter':
        return !!this.openrouterClient
      default:
        return false
    }
  }

  // Get all available providers
  getAvailableProviders(): AIProvider[] {
    const providers: AIProvider[] = []
    
    if (this.isProviderAvailable('openai')) providers.push('openai')
    if (this.isProviderAvailable('gemini')) providers.push('gemini')
    if (this.isProviderAvailable('openrouter')) providers.push('openrouter')
    
    return providers
  }
}

// Export singleton instance
export const aiProviderManager = new AIProviderManager()

// Utility functions
export const getRecommendedModel = (provider: AIProvider, taskType: string): string | null => {
  const models = AI_MODELS[provider]
  const recommendedModel = models.find(model => 
    model.recommended.includes(taskType as any)
  )
  return recommendedModel?.id || models[0]?.id || null
}

export const getCheapestModel = (provider: AIProvider): string | null => {
  const models = AI_MODELS[provider]
  if (models.length === 0) return null
  
  const cheapest = models.reduce((prev, current) => 
    prev.costPer1kTokens < current.costPer1kTokens ? prev : current
  )
  
  return cheapest.id
}

export const getBestModel = (provider: AIProvider): string | null => {
  const models = AI_MODELS[provider]
  if (models.length === 0) return null
  
  // Assume the most expensive model is the best
  const best = models.reduce((prev, current) => 
    prev.costPer1kTokens > current.costPer1kTokens ? prev : current
  )
  
  return best.id
}
