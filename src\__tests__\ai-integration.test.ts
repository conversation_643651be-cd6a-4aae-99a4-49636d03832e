// AI Blog Generator Integration Tests
import { describe, test, expect, beforeAll, afterAll, jest } from '@jest/globals'
import { 
  conductKeywordResearch,
  generateBlogOutline,
  generateBlogContent,
  getOrCreateAISettings,
  validateContentQuality
} from '@/lib/ai'
import { ResearchResult, BlogOutline } from '@/types/ai'

// Mock Firebase Admin
jest.mock('@/lib/firebase-admin', () => ({
  verifyIdToken: jest.fn().mockResolvedValue({ uid: 'test-user-123' })
}))

// Mock environment variables
process.env.OPENAI_API_KEY = 'test-openai-key'
process.env.GOOGLE_AI_API_KEY = 'test-gemini-key'
process.env.OPENROUTER_API_KEY = 'test-openrouter-key'

describe('AI Blog Generator Integration Tests', () => {
  const testUserId = 'test-user-123'
  const testKeywords = ['react', 'typescript', 'web development']
  
  let researchResult: ResearchResult
  let blogOutline: BlogOutline

  beforeAll(async () => {
    // Set up test environment
    console.log('Setting up AI integration tests...')
  })

  afterAll(async () => {
    // Clean up test data
    console.log('Cleaning up AI integration tests...')
  })

  describe('1. AI Settings Management', () => {
    test('should create default AI settings for new user', async () => {
      const settings = await getOrCreateAISettings(testUserId)
      
      expect(settings).toBeDefined()
      expect(settings.user_id).toBe(testUserId)
      expect(settings.provider).toBeDefined()
      expect(settings.models).toBeDefined()
      expect(settings.preferences).toBeDefined()
    })

    test('should validate AI settings structure', async () => {
      const settings = await getOrCreateAISettings(testUserId)
      
      // Check required fields
      expect(settings.provider).toMatch(/^(openai|gemini|openrouter)$/)
      expect(settings.models.research).toBeDefined()
      expect(settings.models.outline).toBeDefined()
      expect(settings.models.content).toBeDefined()
      expect(settings.models.metadata).toBeDefined()
      
      // Check preferences
      expect(typeof settings.preferences.maxTokensPerRequest).toBe('number')
      expect(typeof settings.preferences.temperature).toBe('number')
      expect(typeof settings.preferences.costLimit).toBe('number')
      expect(typeof settings.preferences.enableCaching).toBe('boolean')
    })
  })

  describe('2. Research Phase', () => {
    test('should conduct keyword research successfully', async () => {
      const options = {
        maxSources: 5,
        includeAcademic: false,
        includeNews: true,
        includeBlog: true
      }

      researchResult = await conductKeywordResearch(testKeywords, testUserId, options)
      
      expect(researchResult).toBeDefined()
      expect(researchResult.query).toBe(testKeywords.join(' '))
      expect(researchResult.sources).toBeDefined()
      expect(Array.isArray(researchResult.sources)).toBe(true)
      expect(researchResult.summary).toBeDefined()
      expect(Array.isArray(researchResult.keyPoints)).toBe(true)
      expect(Array.isArray(researchResult.relatedTopics)).toBe(true)
      expect(typeof researchResult.tokensUsed).toBe('number')
      expect(typeof researchResult.cost).toBe('number')
    })

    test('should validate research result structure', () => {
      expect(researchResult.sources.length).toBeGreaterThan(0)
      expect(researchResult.keyPoints.length).toBeGreaterThan(0)
      expect(researchResult.relatedTopics.length).toBeGreaterThan(0)
      
      // Validate source structure
      researchResult.sources.forEach(source => {
        expect(source.url).toBeDefined()
        expect(source.title).toBeDefined()
        expect(source.content).toBeDefined()
        expect(typeof source.relevanceScore).toBe('number')
        expect(source.relevanceScore).toBeGreaterThanOrEqual(0)
        expect(source.relevanceScore).toBeLessThanOrEqual(1)
      })
    })

    test('should handle invalid keywords gracefully', async () => {
      await expect(
        conductKeywordResearch([], testUserId)
      ).rejects.toThrow()

      await expect(
        conductKeywordResearch([''], testUserId)
      ).rejects.toThrow()
    })
  })

  describe('3. Outline Generation Phase', () => {
    test('should generate blog outline from research', async () => {
      const options = {
        targetWordCount: 2000,
        targetAudience: 'intermediate developers',
        contentType: 'guide' as const,
        includeIntroConclusion: true,
        maxChapters: 5
      }

      blogOutline = await generateBlogOutline(
        testKeywords,
        researchResult,
        testUserId,
        options
      )
      
      expect(blogOutline).toBeDefined()
      expect(blogOutline.title).toBeDefined()
      expect(blogOutline.introduction).toBeDefined()
      expect(Array.isArray(blogOutline.chapters)).toBe(true)
      expect(blogOutline.conclusion).toBeDefined()
      expect(blogOutline.metadata).toBeDefined()
    })

    test('should validate outline structure', () => {
      // Check title
      expect(typeof blogOutline.title).toBe('string')
      expect(blogOutline.title.length).toBeGreaterThan(10)
      
      // Check introduction
      expect(blogOutline.introduction.title).toBeDefined()
      expect(blogOutline.introduction.content).toBeDefined()
      expect(Array.isArray(blogOutline.introduction.keyPoints)).toBe(true)
      expect(typeof blogOutline.introduction.wordCount).toBe('number')
      
      // Check chapters
      expect(blogOutline.chapters.length).toBeGreaterThan(0)
      expect(blogOutline.chapters.length).toBeLessThanOrEqual(5)
      
      blogOutline.chapters.forEach(chapter => {
        expect(chapter.title).toBeDefined()
        expect(Array.isArray(chapter.sections)).toBe(true)
        expect(chapter.sections.length).toBeGreaterThan(0)
        expect(typeof chapter.estimatedWordCount).toBe('number')
        
        chapter.sections.forEach(section => {
          expect(section.title).toBeDefined()
          expect(section.content).toBeDefined()
          expect(Array.isArray(section.keyPoints)).toBe(true)
        })
      })
      
      // Check conclusion
      expect(blogOutline.conclusion.title).toBeDefined()
      expect(blogOutline.conclusion.content).toBeDefined()
      
      // Check metadata
      expect(blogOutline.metadata.estimatedWordCount).toBeGreaterThan(0)
      expect(Array.isArray(blogOutline.metadata.categories)).toBe(true)
      expect(Array.isArray(blogOutline.metadata.tags)).toBe(true)
      expect(Array.isArray(blogOutline.metadata.seoKeywords)).toBe(true)
    })

    test('should respect word count constraints', () => {
      const totalEstimatedWords = blogOutline.metadata.estimatedWordCount
      expect(totalEstimatedWords).toBeGreaterThan(1500) // Allow some variance
      expect(totalEstimatedWords).toBeLessThan(3000)
    })
  })

  describe('4. Content Generation Phase', () => {
    test('should generate complete blog content', async () => {
      const options = {
        includeIntroduction: true,
        includeConclusion: true,
        enableInternalLinking: true,
        enableExternalCitations: true,
        maxLinksPerSection: 3,
        writingStyle: 'professional' as const,
        targetAudience: 'intermediate developers'
      }

      const generatedContent = await generateBlogContent(
        blogOutline,
        researchResult,
        testUserId,
        options
      )
      
      expect(generatedContent).toBeDefined()
      expect(generatedContent.title).toBeDefined()
      expect(generatedContent.content).toBeDefined()
      expect(generatedContent.excerpt).toBeDefined()
      expect(generatedContent.metadata).toBeDefined()
      expect(typeof generatedContent.tokensUsed).toBe('number')
      expect(typeof generatedContent.cost).toBe('number')
    }, 60000) // 60 second timeout for content generation

    test('should validate generated content structure', async () => {
      const options = {
        includeIntroduction: true,
        includeConclusion: true,
        enableInternalLinking: false,
        enableExternalCitations: false,
        maxLinksPerSection: 0,
        writingStyle: 'professional' as const,
        targetAudience: 'intermediate developers'
      }

      const generatedContent = await generateBlogContent(
        blogOutline,
        researchResult,
        testUserId,
        options
      )
      
      // Check content structure
      expect(generatedContent.content).toContain('# ')
      expect(generatedContent.content).toContain('## ')
      expect(generatedContent.metadata.wordCount).toBeGreaterThan(500)
      expect(generatedContent.metadata.estimatedReadingTime).toBeGreaterThan(0)
      
      // Check metadata
      expect(Array.isArray(generatedContent.metadata.categories)).toBe(true)
      expect(Array.isArray(generatedContent.metadata.tags)).toBe(true)
    }, 60000)
  })

  describe('5. Content Quality Validation', () => {
    test('should validate content quality', async () => {
      // Generate simple content for testing
      const testContent = `
# Test Blog Post

## Introduction
This is a test blog post to validate the quality validation system.

## Main Content
Here we discuss the main topics with proper structure and formatting.

### Subsection
This subsection provides additional details and examples.

## Conclusion
In conclusion, this post demonstrates proper structure and content.
`

      const validation = await validateContentQuality(
        testContent,
        'Test Blog Post',
        {
          excerpt: 'A test blog post for validation',
          tags: ['test', 'validation'],
          categories: ['Testing'],
          focusKeyword: 'test'
        },
        await getOrCreateAISettings(testUserId),
        testUserId
      )
      
      expect(validation).toBeDefined()
      expect(validation.score).toBeDefined()
      expect(typeof validation.score.overall).toBe('number')
      expect(validation.score.overall).toBeGreaterThanOrEqual(0)
      expect(validation.score.overall).toBeLessThanOrEqual(100)
      
      expect(Array.isArray(validation.issues)).toBe(true)
      expect(Array.isArray(validation.recommendations)).toBe(true)
      expect(typeof validation.isPublishReady).toBe('boolean')
    })

    test('should detect quality issues', async () => {
      const poorContent = 'Short content without proper structure or formatting.'
      
      const validation = await validateContentQuality(
        poorContent,
        'Bad Title',
        {
          excerpt: 'Bad excerpt',
          tags: [],
          categories: [],
          focusKeyword: 'missing'
        },
        await getOrCreateAISettings(testUserId),
        testUserId
      )
      
      expect(validation.score.overall).toBeLessThan(70)
      expect(validation.issues.length).toBeGreaterThan(0)
      expect(validation.isPublishReady).toBe(false)
    })
  })

  describe('6. End-to-End Workflow', () => {
    test('should complete full AI blog generation workflow', async () => {
      const keywords = ['nextjs', 'react', 'tutorial']
      
      // Step 1: Research
      const research = await conductKeywordResearch(keywords, testUserId, {
        maxSources: 3,
        includeAcademic: false,
        includeNews: true,
        includeBlog: true
      })
      
      expect(research).toBeDefined()
      
      // Step 2: Outline
      const outline = await generateBlogOutline(keywords, research, testUserId, {
        targetWordCount: 1500,
        targetAudience: 'beginner developers',
        contentType: 'tutorial',
        maxChapters: 4
      })
      
      expect(outline).toBeDefined()
      
      // Step 3: Content Generation (simplified for testing)
      const content = await generateBlogContent(outline, research, testUserId, {
        includeIntroduction: true,
        includeConclusion: true,
        enableInternalLinking: false,
        enableExternalCitations: false,
        maxLinksPerSection: 0,
        writingStyle: 'casual',
        targetAudience: 'beginner developers'
      })
      
      expect(content).toBeDefined()
      expect(content.content.length).toBeGreaterThan(500)
      
      // Step 4: Quality Validation
      const validation = await validateContentQuality(
        content.content,
        content.title,
        {
          excerpt: content.excerpt,
          tags: content.metadata.tags,
          categories: content.metadata.categories
        },
        await getOrCreateAISettings(testUserId),
        testUserId
      )
      
      expect(validation).toBeDefined()
      expect(validation.score.overall).toBeGreaterThan(0)
      
    }, 120000) // 2 minute timeout for full workflow
  })

  describe('7. Error Handling', () => {
    test('should handle API errors gracefully', async () => {
      // Test with invalid user ID
      await expect(
        conductKeywordResearch(testKeywords, 'invalid-user-id')
      ).rejects.toThrow()
    })

    test('should handle rate limiting', async () => {
      // This would require actual rate limiting implementation
      // For now, just ensure the function exists
      expect(conductKeywordResearch).toBeDefined()
    })
  })
})
