import { NextRequest, NextResponse } from 'next/server'
import { withSecurity, SecurityConfigs } from '@/lib/api-security'
import { getBlogPosts, createBlogPost } from '@/lib/firebase-operations'

// GET /api/posts - Public endpoint to get blog posts
async function handleGet(request: NextRequest) {
  try {
    const posts = await getBlogPosts()
    // Only return published posts for public API
    const publishedPosts = posts.filter(post => post.published)
    
    return NextResponse.json({
      success: true,
      posts: publishedPosts,
      total: publishedPosts.length
    })
  } catch (error) {
    console.error('Error fetching posts:', error)
    return NextResponse.json(
      { error: 'Failed to fetch posts' },
      { status: 500 }
    )
  }
}

// POST /api/posts - Protected endpoint to create blog posts
async function handlePost(request: NextRequest) {
  try {
    const body = await request.json()
    const { title, content, excerpt, tags, categories, featured_image, published } = body

    // Validate required fields
    if (!title || !content) {
      return NextResponse.json(
        { error: 'Title and content are required' },
        { status: 400 }
      )
    }

    // Get user from auth (added by security middleware)
    const authorization = request.headers.get('authorization')
    if (!authorization) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Extract user ID from token (simplified - in real app, get from middleware)
    const userId = 'KNlrg408xubJeEmwFpUbeDQWBgF3' // Admin user for now

    const postData = {
      title,
      content,
      excerpt: excerpt || '',
      tags: tags || [],
      categories: categories || [],
      featured_image: featured_image || '',
      published: published || false,
      scheduled_for: null
    }

    const postId = await createBlogPost(postData, userId)

    return NextResponse.json({
      success: true,
      postId,
      message: 'Blog post created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating post:', error)
    return NextResponse.json(
      { error: 'Failed to create post' },
      { status: 500 }
    )
  }
}

// Apply security middleware
export const GET = withSecurity(handleGet, SecurityConfigs.public)
export const POST = withSecurity(handlePost, SecurityConfigs.protected)
