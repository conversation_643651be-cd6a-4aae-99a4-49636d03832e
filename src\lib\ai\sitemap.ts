// Sitemap Analysis for Internal Linking
'use client'

import { parseString } from 'xml2js'
import { InternalLink } from '@/types/ai'
import { getBlogPosts } from '@/lib/firebase-operations'
import { getProjects } from '@/lib/firebase-server'

export interface SitemapEntry {
  url: string
  title: string
  lastModified?: string
  changeFreq?: string
  priority?: number
  type: 'blog' | 'project' | 'page'
  slug: string
  excerpt?: string
  tags?: string[]
  categories?: string[]
}

export interface LinkingOpportunity {
  targetUrl: string
  targetTitle: string
  anchorText: string
  relevanceScore: number
  context: string
  type: 'blog' | 'project' | 'page'
}

// Sitemap Analyzer Class
export class SitemapAnalyzer {
  private static instance: SitemapAnalyzer
  private sitemapCache: SitemapEntry[] = []
  private lastCacheUpdate: Date | null = null
  private readonly CACHE_DURATION = 1000 * 60 * 60 // 1 hour

  static getInstance(): SitemapAnalyzer {
    if (!SitemapAnalyzer.instance) {
      SitemapAnalyzer.instance = new SitemapAnalyzer()
    }
    return SitemapAnalyzer.instance
  }

  // Generate sitemap from Firebase data
  async generateSitemap(): Promise<SitemapEntry[]> {
    // Check cache first
    if (this.isCacheValid()) {
      return this.sitemapCache
    }

    try {
      const [blogPosts, projects] = await Promise.all([
        getBlogPosts(),
        getProjects()
      ])

      const sitemapEntries: SitemapEntry[] = []

      // Add blog posts
      blogPosts.forEach(post => {
        if (post.published) {
          sitemapEntries.push({
            url: `/blog/${post.slug}`,
            title: post.title,
            lastModified: post.updated_at,
            changeFreq: 'weekly',
            priority: 0.8,
            type: 'blog',
            slug: post.slug,
            excerpt: post.excerpt,
            tags: post.tags,
            categories: post.categories
          })
        }
      })

      // Add projects
      projects.forEach(project => {
        if (project.published) {
          sitemapEntries.push({
            url: `/projects/${project.slug}`,
            title: project.title,
            lastModified: project.updated_at,
            changeFreq: 'monthly',
            priority: 0.7,
            type: 'project',
            slug: project.slug,
            excerpt: project.description,
            tags: project.tags
          })
        }
      })

      // Add static pages
      const staticPages: SitemapEntry[] = [
        {
          url: '/',
          title: 'Home',
          changeFreq: 'weekly',
          priority: 1.0,
          type: 'page',
          slug: 'home'
        },
        {
          url: '/blog',
          title: 'Blog',
          changeFreq: 'daily',
          priority: 0.9,
          type: 'page',
          slug: 'blog'
        },
        {
          url: '/projects',
          title: 'Projects',
          changeFreq: 'weekly',
          priority: 0.9,
          type: 'page',
          slug: 'projects'
        },
        {
          url: '/contact',
          title: 'Contact',
          changeFreq: 'monthly',
          priority: 0.6,
          type: 'page',
          slug: 'contact'
        }
      ]

      sitemapEntries.push(...staticPages)

      // Update cache
      this.sitemapCache = sitemapEntries
      this.lastCacheUpdate = new Date()

      return sitemapEntries
    } catch (error) {
      console.error('Failed to generate sitemap:', error)
      return []
    }
  }

  // Find internal linking opportunities
  async findLinkingOpportunities(
    content: string,
    currentSlug: string,
    keywords: string[] = []
  ): Promise<LinkingOpportunity[]> {
    const sitemap = await this.generateSitemap()
    const opportunities: LinkingOpportunity[] = []

    // Filter out current page
    const availablePages = sitemap.filter(entry => entry.slug !== currentSlug)

    // Analyze content for linking opportunities
    const contentLower = content.toLowerCase()
    const sentences = content.split(/[.!?]+/)

    for (const page of availablePages) {
      const relevanceScore = this.calculateRelevance(
        content,
        page,
        keywords
      )

      if (relevanceScore > 0.3) {
        // Find best anchor text and context
        const linkingContext = this.findBestLinkingContext(
          sentences,
          page,
          contentLower
        )

        if (linkingContext) {
          opportunities.push({
            targetUrl: page.url,
            targetTitle: page.title,
            anchorText: linkingContext.anchorText,
            relevanceScore,
            context: linkingContext.context,
            type: page.type
          })
        }
      }
    }

    // Sort by relevance score
    return opportunities
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, 10) // Limit to top 10 opportunities
  }

  private calculateRelevance(
    content: string,
    page: SitemapEntry,
    keywords: string[]
  ): number {
    let score = 0
    const contentLower = content.toLowerCase()
    const titleLower = page.title.toLowerCase()

    // Check for title mentions
    if (contentLower.includes(titleLower)) {
      score += 0.5
    }

    // Check for keyword overlap
    if (keywords.length > 0) {
      keywords.forEach(keyword => {
        const keywordLower = keyword.toLowerCase()
        if (titleLower.includes(keywordLower)) {
          score += 0.3
        }
        if (page.tags?.some(tag => tag.toLowerCase().includes(keywordLower))) {
          score += 0.2
        }
        if (page.categories?.some(cat => cat.toLowerCase().includes(keywordLower))) {
          score += 0.2
        }
      })
    }

    // Check for tag/category overlap
    if (page.tags) {
      page.tags.forEach(tag => {
        if (contentLower.includes(tag.toLowerCase())) {
          score += 0.1
        }
      })
    }

    if (page.categories) {
      page.categories.forEach(category => {
        if (contentLower.includes(category.toLowerCase())) {
          score += 0.1
        }
      })
    }

    // Boost score for blog posts (more likely to be relevant)
    if (page.type === 'blog') {
      score += 0.1
    }

    return Math.min(score, 1.0)
  }

  private findBestLinkingContext(
    sentences: string[],
    page: SitemapEntry,
    contentLower: string
  ): { anchorText: string; context: string } | null {
    const titleWords = page.title.toLowerCase().split(' ')
    
    for (const sentence of sentences) {
      const sentenceLower = sentence.toLowerCase().trim()
      
      // Look for exact title match
      if (sentenceLower.includes(page.title.toLowerCase())) {
        return {
          anchorText: page.title,
          context: sentence.trim()
        }
      }

      // Look for partial title matches
      for (let i = titleWords.length; i >= 2; i--) {
        const partialTitle = titleWords.slice(0, i).join(' ')
        if (sentenceLower.includes(partialTitle)) {
          return {
            anchorText: partialTitle,
            context: sentence.trim()
          }
        }
      }

      // Look for tag matches
      if (page.tags) {
        for (const tag of page.tags) {
          if (sentenceLower.includes(tag.toLowerCase())) {
            return {
              anchorText: tag,
              context: sentence.trim()
            }
          }
        }
      }
    }

    return null
  }

  // Insert internal links into content
  insertInternalLinks(
    content: string,
    opportunities: LinkingOpportunity[],
    maxLinks: number = 5
  ): string {
    let modifiedContent = content
    let linksInserted = 0

    // Sort opportunities by relevance
    const sortedOpportunities = opportunities
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, maxLinks)

    for (const opportunity of sortedOpportunities) {
      if (linksInserted >= maxLinks) break

      // Create markdown link
      const markdownLink = `[${opportunity.anchorText}](${opportunity.targetUrl})`
      
      // Replace first occurrence of anchor text (case insensitive)
      const regex = new RegExp(`\\b${opportunity.anchorText}\\b`, 'i')
      const match = modifiedContent.match(regex)
      
      if (match && !modifiedContent.includes(markdownLink)) {
        modifiedContent = modifiedContent.replace(regex, markdownLink)
        linksInserted++
      }
    }

    return modifiedContent
  }

  // Get related content suggestions
  async getRelatedContent(
    currentSlug: string,
    keywords: string[] = [],
    limit: number = 5
  ): Promise<SitemapEntry[]> {
    const sitemap = await this.generateSitemap()
    const relatedContent: Array<SitemapEntry & { relevanceScore: number }> = []

    for (const entry of sitemap) {
      if (entry.slug === currentSlug) continue

      const relevanceScore = this.calculateRelevance(
        keywords.join(' '),
        entry,
        keywords
      )

      if (relevanceScore > 0.2) {
        relatedContent.push({ ...entry, relevanceScore })
      }
    }

    return relatedContent
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, limit)
      .map(({ relevanceScore, ...entry }) => entry)
  }

  private isCacheValid(): boolean {
    if (!this.lastCacheUpdate || this.sitemapCache.length === 0) {
      return false
    }
    
    const now = new Date()
    const timeDiff = now.getTime() - this.lastCacheUpdate.getTime()
    return timeDiff < this.CACHE_DURATION
  }

  // Clear cache
  clearCache(): void {
    this.sitemapCache = []
    this.lastCacheUpdate = null
  }

  // Export sitemap as XML
  async exportSitemapXML(): Promise<string> {
    const sitemap = await this.generateSitemap()
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://ernestromelo.com'

    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n'
    xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">\n'

    for (const entry of sitemap) {
      xml += '  <url>\n'
      xml += `    <loc>${baseUrl}${entry.url}</loc>\n`
      if (entry.lastModified) {
        xml += `    <lastmod>${entry.lastModified}</lastmod>\n`
      }
      if (entry.changeFreq) {
        xml += `    <changefreq>${entry.changeFreq}</changefreq>\n`
      }
      if (entry.priority) {
        xml += `    <priority>${entry.priority}</priority>\n`
      }
      xml += '  </url>\n'
    }

    xml += '</urlset>'
    return xml
  }
}

// Export singleton instance
export const sitemapAnalyzer = SitemapAnalyzer.getInstance()

// Utility functions
export const findInternalLinks = async (
  content: string,
  currentSlug: string,
  keywords: string[] = []
): Promise<InternalLink[]> => {
  const opportunities = await sitemapAnalyzer.findLinkingOpportunities(
    content,
    currentSlug,
    keywords
  )

  return opportunities.map(opp => ({
    text: opp.anchorText,
    url: opp.targetUrl,
    relevanceScore: opp.relevanceScore,
    context: opp.context
  }))
}

export const insertAutoLinks = async (
  content: string,
  currentSlug: string,
  keywords: string[] = [],
  maxLinks: number = 5
): Promise<string> => {
  const opportunities = await sitemapAnalyzer.findLinkingOpportunities(
    content,
    currentSlug,
    keywords
  )

  return sitemapAnalyzer.insertInternalLinks(content, opportunities, maxLinks)
}
