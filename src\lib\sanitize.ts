'use client'

import DOMPurify from 'dompurify'

// Configure DOMPurify for safe HTML sanitization
const configureDOMPurify = () => {
  if (typeof window !== 'undefined') {
    // Allow safe HTML tags and attributes for blog content
    DOMPurify.addHook('beforeSanitizeElements', (node) => {
      // Remove any script tags completely
      if (node.tagName === 'SCRIPT') {
        node.remove()
        return node
      }
    })

    // Configure allowed tags and attributes
    const config = {
      ALLOWED_TAGS: [
        // Text formatting
        'p', 'br', 'strong', 'b', 'em', 'i', 'u', 'strike', 'del', 'ins',
        // Headings
        'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        // Lists
        'ul', 'ol', 'li',
        // Links and media
        'a', 'img',
        // Code
        'code', 'pre',
        // Tables
        'table', 'thead', 'tbody', 'tr', 'th', 'td',
        // Quotes and divs
        'blockquote', 'div', 'span',
        // Line breaks
        'hr'
      ],
      ALLOWED_ATTR: [
        // Links
        'href', 'title', 'target', 'rel',
        // Images
        'src', 'alt', 'width', 'height',
        // General
        'class', 'id',
        // Code highlighting
        'data-language'
      ],
      ALLOWED_URI_REGEXP: /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp|data):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i,
      // Remove any javascript: or data: URIs except for images
      FORBID_ATTR: ['onerror', 'onload', 'onclick', 'onmouseover', 'onfocus', 'onblur'],
      FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'button'],
      // Keep content of forbidden elements but remove the tags
      KEEP_CONTENT: true,
      // Remove empty elements
      REMOVE_EMPTY: ['script', 'style'],
      // Allow data URIs for images only
      ALLOW_DATA_ATTR: false
    }

    return config
  }
  return {}
}

/**
 * Sanitize HTML content to prevent XSS attacks
 * @param html - Raw HTML string to sanitize
 * @param options - Optional DOMPurify configuration overrides
 * @returns Sanitized HTML string safe for rendering
 */
export function sanitizeHtml(html: string, options?: any): string {
  if (typeof window === 'undefined') {
    // Server-side: aggressive sanitization
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
      .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
      .replace(/<embed[^>]*>/gi, '')
      .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/vbscript:/gi, '')
      .replace(/data:text\/html/gi, '')
  }

  const config = { ...configureDOMPurify(), ...options }
  const sanitized = DOMPurify.sanitize(html, config)

  // Additional client-side checks
  if (sanitized.includes('<script') || sanitized.includes('javascript:') || sanitized.includes('onerror')) {
    console.warn('Potentially dangerous content detected and removed')
    return sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                    .replace(/javascript:/gi, '')
                    .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
  }

  return sanitized
}

/**
 * Sanitize HTML specifically for blog content
 * Allows more formatting tags but still prevents XSS
 */
export function sanitizeBlogContent(html: string): string {
  return sanitizeHtml(html, {
    ALLOWED_TAGS: [
      'p', 'br', 'strong', 'b', 'em', 'i', 'u', 'strike', 'del', 'ins',
      'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'ul', 'ol', 'li',
      'a', 'img',
      'code', 'pre',
      'table', 'thead', 'tbody', 'tr', 'th', 'td',
      'blockquote', 'div', 'span',
      'hr'
    ],
    ALLOWED_ATTR: [
      'href', 'title', 'target', 'rel',
      'src', 'alt', 'width', 'height',
      'class', 'id',
      'data-language'
    ]
  })
}

/**
 * Sanitize HTML for comments (more restrictive)
 * Only allows basic formatting
 */
export function sanitizeCommentContent(html: string): string {
  return sanitizeHtml(html, {
    ALLOWED_TAGS: ['p', 'br', 'strong', 'b', 'em', 'i', 'a'],
    ALLOWED_ATTR: ['href', 'title'],
    FORBID_TAGS: ['script', 'img', 'video', 'audio', 'iframe', 'object', 'embed']
  })
}

/**
 * Strip all HTML tags and return plain text
 * Use for titles, excerpts, and other text-only content
 */
export function stripHtml(html: string): string {
  if (typeof window === 'undefined') {
    return html.replace(/<[^>]*>/g, '')
  }
  
  return DOMPurify.sanitize(html, { 
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true
  })
}

/**
 * Escape HTML entities for safe display
 * Use when you want to show HTML code as text
 */
export function escapeHtml(text: string): string {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

/**
 * Validate if content contains potentially dangerous elements
 * Returns true if content appears safe, false if suspicious
 */
export function validateContentSafety(content: string): boolean {
  const dangerousPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+\s*=/i, // Event handlers like onclick, onload
    /<iframe/i,
    /<object/i,
    /<embed/i,
    /data:text\/html/i,
    /vbscript:/i
  ]

  return !dangerousPatterns.some(pattern => pattern.test(content))
}

/**
 * Safe component for rendering sanitized HTML
 * Use this instead of dangerouslySetInnerHTML
 */
export function createSafeHTML(html: string, sanitizer = sanitizeHtml) {
  return { __html: sanitizer(html) }
}

// Export configured DOMPurify instance for advanced usage
export { DOMPurify }

// Default export
export default {
  sanitizeHtml,
  sanitizeBlogContent,
  sanitizeCommentContent,
  stripHtml,
  escapeHtml,
  validateContentSafety,
  createSafeHTML
}
