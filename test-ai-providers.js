// AI Provider Testing Script
// Run this to verify all AI providers are working correctly

const { config } = require('dotenv')
config({ path: '.env.local' })

async function testAIProviders() {
  console.log('🧪 Testing AI Provider Configuration...\n')
  
  // Check environment variables
  const requiredEnvVars = [
    'OPENAI_API_KEY',
    'GOOGLE_AI_API_KEY', 
    'OPENROUTER_API_KEY',
    'NEXT_PUBLIC_FIREBASE_PROJECT_ID'
  ]
  
  console.log('📋 Environment Variables Check:')
  let allEnvVarsPresent = true
  
  requiredEnvVars.forEach(envVar => {
    const value = process.env[envVar]
    const status = value ? '✅' : '❌'
    const displayValue = value ? `${value.substring(0, 10)}...` : 'NOT SET'
    console.log(`${status} ${envVar}: ${displayValue}`)
    if (!value) allEnvVarsPresent = false
  })
  
  if (!allEnvVarsPresent) {
    console.log('\n❌ Some environment variables are missing!')
    return false
  }
  
  console.log('\n✅ All environment variables are configured!')
  
  // Test OpenAI API
  console.log('\n🤖 Testing OpenAI API...')
  try {
    const response = await fetch('https://api.openai.com/v1/models', {
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      console.log('✅ OpenAI API: Connection successful')
    } else {
      console.log(`❌ OpenAI API: Error ${response.status}`)
    }
  } catch (error) {
    console.log(`❌ OpenAI API: ${error.message}`)
  }
  
  // Test Google AI (Gemini) API
  console.log('\n🧠 Testing Google AI (Gemini) API...')
  try {
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models?key=${process.env.GOOGLE_AI_API_KEY}`)
    
    if (response.ok) {
      console.log('✅ Google AI API: Connection successful')
    } else {
      console.log(`❌ Google AI API: Error ${response.status}`)
    }
  } catch (error) {
    console.log(`❌ Google AI API: ${error.message}`)
  }
  
  // Test OpenRouter API
  console.log('\n🔀 Testing OpenRouter API...')
  try {
    const response = await fetch('https://openrouter.ai/api/v1/models', {
      headers: {
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      console.log('✅ OpenRouter API: Connection successful')
    } else {
      console.log(`❌ OpenRouter API: Error ${response.status}`)
    }
  } catch (error) {
    console.log(`❌ OpenRouter API: ${error.message}`)
  }
  
  console.log('\n🎉 AI Provider testing completed!')
  return true
}

// Run the test
testAIProviders().catch(console.error)
