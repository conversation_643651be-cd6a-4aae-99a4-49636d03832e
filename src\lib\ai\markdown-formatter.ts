// Advanced Markdown Formatting System
'use client'

import { OutlineChapter, ResearchSource, AIRequest, AISettings } from '@/types/ai'
import { optimizedAIManager } from './optimization'

export interface MarkdownElement {
  type: 'table' | 'quote' | 'codeblock' | 'callout' | 'list' | 'image'
  content: string
  metadata?: {
    language?: string
    caption?: string
    source?: string
    author?: string
  }
}

export interface FormattingOptions {
  includeTableOfContents: boolean
  useCallouts: boolean
  addImagePlaceholders: boolean
  codeLanguage: string
  quoteStyle: 'blockquote' | 'callout'
  tableStyle: 'simple' | 'advanced'
}

// Markdown Formatter Class
export class MarkdownFormatter {
  private static instance: MarkdownFormatter

  static getInstance(): MarkdownFormatter {
    if (!MarkdownFormatter.instance) {
      MarkdownFormatter.instance = new MarkdownFormatter()
    }
    return MarkdownFormatter.instance
  }

  // Generate advanced table based on chapter content
  async generateTable(
    chapterTitle: string,
    chapterContent: string,
    sources: ResearchSource[],
    settings: AISettings,
    userId: string,
    style: 'simple' | 'advanced' = 'advanced'
  ): Promise<string> {
    const tablePrompt = `
Create a relevant data table for this chapter:

Chapter: ${chapterTitle}
Content Context: ${chapterContent.substring(0, 500)}

Research Data:
${sources.slice(0, 3).map(source => `- ${source.title}: ${source.content.substring(0, 200)}`).join('\n')}

Instructions:
1. Create a table that adds value to the chapter content
2. Use real or realistic data when possible
3. Include 3-5 columns and 4-8 rows
4. Make headers descriptive and clear
5. Ensure data is relevant and accurate
${style === 'advanced' ? '6. Include a caption explaining the table' : ''}

Format as Markdown table with proper alignment.
${style === 'advanced' ? 'Include a caption line after the table.' : ''}
`

    const request: AIRequest = {
      provider: settings.provider,
      model: settings.models.content,
      prompt: tablePrompt,
      maxTokens: 800,
      temperature: 0.3,
      systemPrompt: 'You are a data analyst creating informative tables for blog content.'
    }

    try {
      const response = await optimizedAIManager.generateOptimizedResponse(request, userId, settings)
      return this.formatTable(response.content, style)
    } catch (error) {
      console.error('Failed to generate table:', error)
      return this.getFallbackTable(chapterTitle)
    }
  }

  // Generate expert quote or testimonial
  async generateQuote(
    chapterTitle: string,
    chapterContent: string,
    sources: ResearchSource[],
    settings: AISettings,
    userId: string,
    style: 'blockquote' | 'callout' = 'blockquote'
  ): Promise<string> {
    const quotePrompt = `
Create an expert quote or testimonial relevant to this chapter:

Chapter: ${chapterTitle}
Content Context: ${chapterContent.substring(0, 500)}

Available Sources:
${sources.slice(0, 3).map(source => `- ${source.title} by ${source.author || 'Expert'}`).join('\n')}

Instructions:
1. Create a meaningful quote that supports the chapter content
2. Make it sound authentic and professional
3. Attribute it to a relevant expert or industry professional
4. Keep it between 1-3 sentences
5. Ensure it adds value and credibility to the content

Format as a proper quote with attribution.
`

    const request: AIRequest = {
      provider: settings.provider,
      model: settings.models.content,
      prompt: quotePrompt,
      maxTokens: 300,
      temperature: 0.5,
      systemPrompt: 'You are creating authentic expert quotes for blog content.'
    }

    try {
      const response = await optimizedAIManager.generateOptimizedResponse(request, userId, settings)
      return this.formatQuote(response.content, style)
    } catch (error) {
      console.error('Failed to generate quote:', error)
      return this.getFallbackQuote(chapterTitle, style)
    }
  }

  // Generate code block with explanation
  async generateCodeBlock(
    chapterTitle: string,
    chapterContent: string,
    language: string,
    settings: AISettings,
    userId: string
  ): Promise<string> {
    const codePrompt = `
Create a relevant code example for this chapter:

Chapter: ${chapterTitle}
Content Context: ${chapterContent.substring(0, 500)}
Programming Language: ${language}

Instructions:
1. Create a practical code example that demonstrates the concepts
2. Keep it concise but complete (10-30 lines)
3. Include comments explaining key parts
4. Make it production-ready and follow best practices
5. Ensure it's directly relevant to the chapter content

Provide the code block followed by a brief explanation of what it does.
`

    const request: AIRequest = {
      provider: settings.provider,
      model: settings.models.content,
      prompt: codePrompt,
      maxTokens: 1000,
      temperature: 0.2,
      systemPrompt: 'You are a senior developer creating educational code examples.'
    }

    try {
      const response = await optimizedAIManager.generateOptimizedResponse(request, userId, settings)
      return this.formatCodeBlock(response.content, language)
    } catch (error) {
      console.error('Failed to generate code block:', error)
      return this.getFallbackCodeBlock(chapterTitle, language)
    }
  }

  // Generate callout boxes for important information
  generateCallout(content: string, type: 'info' | 'warning' | 'tip' | 'note' = 'info'): string {
    const icons = {
      info: 'ℹ️',
      warning: '⚠️',
      tip: '💡',
      note: '📝'
    }

    const styles = {
      info: 'blue',
      warning: 'yellow',
      tip: 'green',
      note: 'gray'
    }

    return `
> ${icons[type]} **${type.toUpperCase()}**
> 
> ${content.split('\n').join('\n> ')}
`
  }

  // Generate table of contents
  generateTableOfContents(content: string): string {
    const headers = content.match(/^#{2,4}\s+(.+)$/gm) || []
    
    if (headers.length === 0) return ''

    let toc = '## Table of Contents\n\n'
    
    headers.forEach(header => {
      const level = (header.match(/#/g) || []).length
      const title = header.replace(/^#+\s+/, '')
      const anchor = title.toLowerCase().replace(/[^a-z0-9\s]/g, '').replace(/\s+/g, '-')
      const indent = '  '.repeat(level - 2)
      
      toc += `${indent}- [${title}](#${anchor})\n`
    })

    return toc + '\n'
  }

  // Format content with advanced markdown elements
  async enhanceContent(
    content: string,
    chapter: OutlineChapter,
    sources: ResearchSource[],
    settings: AISettings,
    userId: string,
    options: FormattingOptions
  ): Promise<string> {
    let enhancedContent = content

    try {
      // Add table if required
      if (chapter.includeTable) {
        const table = await this.generateTable(
          chapter.title,
          content,
          sources,
          settings,
          userId,
          options.tableStyle
        )
        enhancedContent = this.insertElementInContent(enhancedContent, table, 'table')
      }

      // Add quote if required
      if (chapter.includeQuote) {
        const quote = await this.generateQuote(
          chapter.title,
          content,
          sources,
          settings,
          userId,
          options.quoteStyle
        )
        enhancedContent = this.insertElementInContent(enhancedContent, quote, 'quote')
      }

      // Add code block if required
      if (chapter.includeCodeBlock) {
        const codeBlock = await this.generateCodeBlock(
          chapter.title,
          content,
          options.codeLanguage,
          settings,
          userId
        )
        enhancedContent = this.insertElementInContent(enhancedContent, codeBlock, 'code')
      }

      // Add callouts for important information
      if (options.useCallouts) {
        enhancedContent = this.addCallouts(enhancedContent)
      }

      // Add image placeholders
      if (options.addImagePlaceholders) {
        enhancedContent = this.addImagePlaceholders(enhancedContent, chapter.title)
      }

    } catch (error) {
      console.error('Failed to enhance content:', error)
    }

    return enhancedContent
  }

  private formatTable(content: string, style: 'simple' | 'advanced'): string {
    // Ensure proper table formatting
    let table = content.trim()
    
    // Add proper spacing around table
    if (!table.startsWith('\n')) table = '\n' + table
    if (!table.endsWith('\n')) table = table + '\n'
    
    return table
  }

  private formatQuote(content: string, style: 'blockquote' | 'callout'): string {
    const cleanContent = content.trim().replace(/^["']|["']$/g, '')
    
    if (style === 'callout') {
      return this.generateCallout(cleanContent, 'note')
    }
    
    // Extract quote and attribution
    const parts = cleanContent.split(' - ')
    const quote = parts[0]
    const attribution = parts[1] || 'Industry Expert'
    
    return `\n> "${quote}"\n> \n> — ${attribution}\n`
  }

  private formatCodeBlock(content: string, language: string): string {
    // Extract code and explanation
    const codeMatch = content.match(/```[\s\S]*?```/)
    let code = codeMatch ? codeMatch[0] : content
    
    // Ensure proper language tag
    if (!code.includes('```' + language)) {
      code = code.replace(/```(\w+)?/, `\`\`\`${language}`)
    }
    
    // Add explanation if not present
    const explanation = content.replace(/```[\s\S]*?```/, '').trim()
    
    return `\n${code}\n\n${explanation ? explanation : 'This code demonstrates the key concepts discussed above.'}\n`
  }

  private insertElementInContent(content: string, element: string, type: 'table' | 'quote' | 'code'): string {
    const paragraphs = content.split('\n\n')
    
    // Find a good insertion point (after first or second paragraph)
    const insertIndex = Math.min(2, Math.floor(paragraphs.length / 2))
    
    paragraphs.splice(insertIndex, 0, element.trim())
    
    return paragraphs.join('\n\n')
  }

  private addCallouts(content: string): string {
    // Look for sentences that could be callouts
    const importantPatterns = [
      /Important[ly]?[:\s]/gi,
      /Note that[:\s]/gi,
      /Remember[:\s]/gi,
      /Keep in mind[:\s]/gi,
      /Pro tip[:\s]/gi,
      /Warning[:\s]/gi
    ]

    let enhanced = content

    importantPatterns.forEach(pattern => {
      enhanced = enhanced.replace(pattern, (match) => {
        const type = match.toLowerCase().includes('warning') ? 'warning' :
                    match.toLowerCase().includes('tip') ? 'tip' : 'info'
        return `\n${this.generateCallout(match, type)}\n`
      })
    })

    return enhanced
  }

  private addImagePlaceholders(content: string, chapterTitle: string): string {
    const paragraphs = content.split('\n\n')
    
    // Add image placeholder after first paragraph
    if (paragraphs.length > 1) {
      const imagePlaceholder = `\n![${chapterTitle} illustration](placeholder-image-url)\n*Caption: Visual representation of ${chapterTitle.toLowerCase()}*\n`
      paragraphs.splice(1, 0, imagePlaceholder)
    }
    
    return paragraphs.join('\n\n')
  }

  private getFallbackTable(chapterTitle: string): string {
    return `
| Feature | Description | Benefits |
|---------|-------------|----------|
| Feature A | Core functionality | Improved efficiency |
| Feature B | Advanced capabilities | Better performance |
| Feature C | User experience | Enhanced usability |

*Table: Key features related to ${chapterTitle}*
`
  }

  private getFallbackQuote(chapterTitle: string, style: 'blockquote' | 'callout'): string {
    const quote = `This approach has proven to be highly effective in real-world applications and continues to be a best practice in the industry.`
    
    if (style === 'callout') {
      return this.generateCallout(quote, 'note')
    }
    
    return `\n> "${quote}"\n> \n> — Industry Expert\n`
  }

  private getFallbackCodeBlock(chapterTitle: string, language: string): string {
    const codeExamples = {
      javascript: `\`\`\`javascript
// Example implementation for ${chapterTitle}
function implementSolution() {
  const result = processData();
  return result.filter(item => item.isValid);
}

// Usage
const solution = implementSolution();
console.log('Solution implemented:', solution);
\`\`\``,
      python: `\`\`\`python
# Example implementation for ${chapterTitle}
def implement_solution():
    result = process_data()
    return [item for item in result if item.is_valid]

# Usage
solution = implement_solution()
print(f"Solution implemented: {solution}")
\`\`\``,
      typescript: `\`\`\`typescript
// Example implementation for ${chapterTitle}
interface Solution {
  id: string;
  isValid: boolean;
}

function implementSolution(): Solution[] {
  const result = processData();
  return result.filter(item => item.isValid);
}

// Usage
const solution: Solution[] = implementSolution();
console.log('Solution implemented:', solution);
\`\`\``
    }

    const code = codeExamples[language as keyof typeof codeExamples] || codeExamples.javascript
    return `\n${code}\n\nThis code demonstrates a practical implementation of the concepts discussed in this section.\n`
  }
}

// Export singleton instance
export const markdownFormatter = MarkdownFormatter.getInstance()

// Utility functions
export const enhanceContentWithMarkdown = async (
  content: string,
  chapter: OutlineChapter,
  sources: ResearchSource[],
  settings: AISettings,
  userId: string,
  options?: FormattingOptions
): Promise<string> => {
  const defaultOptions: FormattingOptions = {
    includeTableOfContents: false,
    useCallouts: true,
    addImagePlaceholders: false,
    codeLanguage: 'javascript',
    quoteStyle: 'blockquote',
    tableStyle: 'advanced'
  }

  return await markdownFormatter.enhanceContent(
    content,
    chapter,
    sources,
    settings,
    userId,
    { ...defaultOptions, ...options }
  )
}

export const generateMarkdownElement = async (
  type: 'table' | 'quote' | 'codeblock',
  context: string,
  sources: ResearchSource[],
  settings: AISettings,
  userId: string,
  options?: any
): Promise<string> => {
  switch (type) {
    case 'table':
      return await markdownFormatter.generateTable(
        'Chapter',
        context,
        sources,
        settings,
        userId,
        options?.style || 'advanced'
      )
    case 'quote':
      return await markdownFormatter.generateQuote(
        'Chapter',
        context,
        sources,
        settings,
        userId,
        options?.style || 'blockquote'
      )
    case 'codeblock':
      return await markdownFormatter.generateCodeBlock(
        'Chapter',
        context,
        options?.language || 'javascript',
        settings,
        userId
      )
    default:
      return ''
  }
}
