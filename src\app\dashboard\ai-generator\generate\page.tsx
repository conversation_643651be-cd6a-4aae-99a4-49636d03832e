'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/AuthProvider'
import { useRouter } from 'next/navigation'
import { 
  PencilIcon, 
  CheckIcon,
  XMarkIcon,
  SparklesIcon,
  DocumentDuplicateIcon,
  EyeIcon
} from '@heroicons/react/24/outline'
import {
  DashboardCard,
  DashboardCardContent,
  DashboardCardDescription,
  DashboardCardHeader,
  DashboardCardTitle
} from '@/components/dashboard/DashboardCard'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { createBlogPost } from '@/lib/firebase-operations'
import { BlogOutline, ResearchResult } from '@/types/ai'
import { generateBlogContent } from '@/lib/ai/content-generator'
import { getOrCreateAISettings } from '@/lib/ai'

export default function GeneratePage() {
  const { user } = useAuth()
  const router = useRouter()
  const [outline, setOutline] = useState<BlogOutline | null>(null)
  const [researchResult, setResearchResult] = useState<ResearchResult | null>(null)
  const [generating, setGenerating] = useState(false)
  const [generatedContent, setGeneratedContent] = useState('')
  const [currentChapter, setCurrentChapter] = useState(0)
  const [totalChapters, setTotalChapters] = useState(0)
  const [progress, setProgress] = useState(0)
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(true)
  const [savedAsDraft, setSavedAsDraft] = useState(false)

  useEffect(() => {
    loadOutline()
  }, [])

  const loadOutline = async () => {
    try {
      const storedOutline = sessionStorage.getItem('ai-outline')
      const storedResearch = sessionStorage.getItem('ai-research-result')

      if (!storedOutline) {
        setError('No outline found. Please start from the outline step.')
        return
      }

      if (!storedResearch) {
        setError('No research data found. Please start from the research step.')
        return
      }

      const parsedOutline = JSON.parse(storedOutline)
      const parsedResearch = JSON.parse(storedResearch)
      setOutline(parsedOutline)
      setResearchResult(parsedResearch)
      setTotalChapters(parsedOutline.chapters.length)
    } catch (error) {
      console.error('Error loading outline:', error)
      setError('Failed to load outline data')
    } finally {
      setLoading(false)
    }
  }

  const generateContent = async () => {
    if (!outline || !researchResult || !user) return

    setGenerating(true)
    setError('')
    setProgress(0)
    setCurrentChapter(0)

    try {
      // Use real AI content generation
      const generatedResult = await generateBlogContent(
        outline,
        researchResult,
        user.uid,
        {
          includeIntroduction: true,
          includeConclusion: true,
          enableInternalLinking: true,
          enableExternalCitations: true,
          maxLinksPerSection: 3,
          writingStyle: 'professional',
          targetAudience: 'intermediate developers'
        },
        (progressUpdate) => {
          setProgress(progressUpdate.progress)
          setCurrentChapter(progressUpdate.currentChapter || 0)
        }
      )

      setGeneratedContent(generatedResult.content)

      // Auto-save as draft with generated metadata
      await saveAsDraft(generatedResult.content, generatedResult.metadata)

    } catch (error) {
      console.error('Content generation failed:', error)
      setError(`Content generation failed: ${error}`)
    } finally {
      setGenerating(false)
    }
  }



  const saveAsDraft = async (content: string, metadata?: any) => {
    if (!user || !outline) return

    try {
      const postData = {
        title: outline.title,
        content: content,
        excerpt: metadata?.excerpt || outline.introduction.content.substring(0, 200) + '...',
        featured_image: '',
        published: false, // Save as draft
        scheduled_for: new Date().toISOString(),
        tags: metadata?.tags || outline.metadata.tags,
        categories: metadata?.categories || outline.metadata.categories
      }

      const postId = await createBlogPost(postData, user.uid)
      setSavedAsDraft(true)

      // Store the post ID for potential editing
      sessionStorage.setItem('ai-generated-post-id', postId)

    } catch (error) {
      console.error('Failed to save as draft:', error)
      setError('Failed to save content as draft')
    }
  }

  const viewDraft = () => {
    const postId = sessionStorage.getItem('ai-generated-post-id')
    if (postId) {
      router.push(`/dashboard/posts/${postId}/edit`)
    }
  }

  const goBackToOutline = () => {
    router.push('/dashboard/ai-generator/outline')
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 dark:text-gray-400">Loading content generator...</p>
        </div>
      </div>
    )
  }

  if (error && !outline) {
    return (
      <div className="text-center py-12">
        <XMarkIcon className="w-12 h-12 text-red-500 mx-auto mb-4" />
        <h2 className="text-xl font-semibold mb-2">Missing Outline Data</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
        <Button onClick={goBackToOutline}>
          ← Back to Outline
        </Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight dashboard-text flex items-center gap-2">
            <PencilIcon className="w-7 h-7 text-green-600" />
            Content Generation
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Generate AI-powered content in batches with rich formatting
          </p>
        </div>
        <Button variant="outline" onClick={goBackToOutline}>
          ← Back to Outline
        </Button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-start">
            <XMarkIcon className="w-5 h-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
            <div>
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Generation Error
              </h3>
              <p className="mt-1 text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Success Message */}
      {savedAsDraft && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <CheckIcon className="w-5 h-5 text-green-500 mr-3" />
              <div>
                <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                  Content Saved as Draft
                </h3>
                <p className="text-sm text-green-700 dark:text-green-300">
                  Your AI-generated blog post has been saved as a draft and is ready for review.
                </p>
              </div>
            </div>
            <Button onClick={viewDraft} size="sm">
              <EyeIcon className="w-4 h-4 mr-2" />
              View Draft
            </Button>
          </div>
        </div>
      )}

      {/* Outline Summary */}
      {outline && (
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle>Content Overview</DashboardCardTitle>
            <DashboardCardDescription>
              {outline.title} • {outline.chapters.length} chapters • ~{outline.metadata.estimatedWordCount} words
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <h3 className="font-medium mb-2">Structure</h3>
                <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                  <li>• Introduction</li>
                  {outline.chapters.map((chapter, index) => (
                    <li key={index}>• {chapter.title}</li>
                  ))}
                  <li>• Conclusion</li>
                </ul>
              </div>
              <div>
                <h3 className="font-medium mb-2">Categories</h3>
                <div className="flex flex-wrap gap-1">
                  {outline.metadata.categories.map((cat, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {cat}
                    </Badge>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="font-medium mb-2">Tags</h3>
                <div className="flex flex-wrap gap-1">
                  {outline.metadata.tags.slice(0, 6).map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </DashboardCardContent>
        </DashboardCard>
      )}

      {/* Generation Progress */}
      {generating && (
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle className="flex items-center gap-2">
              <SparklesIcon className="w-5 h-5 text-blue-500 animate-pulse" />
              Generating Content...
            </DashboardCardTitle>
            <DashboardCardDescription>
              Chapter {currentChapter} of {totalChapters} • {progress}% complete
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            <div className="space-y-4">
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                <div 
                  className="bg-blue-600 h-3 rounded-full transition-all duration-500 ease-out"
                  style={{ width: `${progress}%` }}
                />
              </div>
              <div className="text-center text-sm text-gray-600 dark:text-gray-400">
                Generating content in 2-chapter batches for optimal quality...
              </div>
            </div>
          </DashboardCardContent>
        </DashboardCard>
      )}

      {/* Generated Content Preview */}
      {generatedContent && (
        <DashboardCard>
          <DashboardCardHeader>
            <DashboardCardTitle className="flex items-center gap-2">
              <CheckIcon className="w-5 h-5 text-green-500" />
              Generated Content
            </DashboardCardTitle>
            <DashboardCardDescription>
              Content generated successfully and saved as draft
            </DashboardCardDescription>
          </DashboardCardHeader>
          <DashboardCardContent>
            <div className="space-y-4">
              <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 max-h-96 overflow-y-auto">
                <pre className="text-sm whitespace-pre-wrap font-mono">
                  {generatedContent.substring(0, 1000)}
                  {generatedContent.length > 1000 && '...'}
                </pre>
              </div>
              <div className="flex gap-3">
                <Button onClick={viewDraft} className="flex-1">
                  <EyeIcon className="w-4 h-4 mr-2" />
                  Edit in Blog Editor
                </Button>
                <Button variant="outline" onClick={() => router.push('/dashboard/posts')}>
                  <DocumentDuplicateIcon className="w-4 h-4 mr-2" />
                  View All Posts
                </Button>
              </div>
            </div>
          </DashboardCardContent>
        </DashboardCard>
      )}

      {/* Generate Content Button */}
      {!generatedContent && !generating && (
        <div className="flex justify-center">
          <Button 
            onClick={generateContent}
            disabled={!outline}
            size="lg"
            className="px-8"
          >
            <PencilIcon className="w-5 h-5 mr-2" />
            Generate Content
          </Button>
        </div>
      )}
    </div>
  )
}
