// AI Blog Generator System Test Script
import { 
  conductKeywordResearch,
  generateBlogOutline,
  generateBlogContent,
  getOrCreateAISettings,
  validateContentQuality,
  getUserPerformanceStats,
  trackOperation,
  completeOperation
} from '@/lib/ai'

// Test configuration
const TEST_CONFIG = {
  userId: 'test-user-' + Date.now(),
  keywords: ['nextjs', 'react', 'typescript'],
  timeout: 120000 // 2 minutes
}

// Test results interface
interface TestResult {
  name: string
  success: boolean
  duration: number
  error?: string
  data?: any
}

class AISystemTester {
  private results: TestResult[] = []

  async runTest(name: string, testFn: () => Promise<any>): Promise<TestResult> {
    console.log(`🧪 Running test: ${name}`)
    const startTime = Date.now()
    
    try {
      const data = await testFn()
      const duration = Date.now() - startTime
      
      const result: TestResult = {
        name,
        success: true,
        duration,
        data
      }
      
      this.results.push(result)
      console.log(`✅ ${name} - Passed (${duration}ms)`)
      return result
      
    } catch (error) {
      const duration = Date.now() - startTime
      
      const result: TestResult = {
        name,
        success: false,
        duration,
        error: error instanceof Error ? error.message : String(error)
      }
      
      this.results.push(result)
      console.log(`❌ ${name} - Failed (${duration}ms): ${result.error}`)
      return result
    }
  }

  async runAllTests(): Promise<void> {
    console.log('🚀 Starting AI Blog Generator System Tests')
    console.log('=' .repeat(50))

    // Test 1: AI Settings
    await this.runTest('AI Settings Creation', async () => {
      const settings = await getOrCreateAISettings(TEST_CONFIG.userId)
      
      if (!settings.user_id || !settings.provider || !settings.models) {
        throw new Error('Invalid settings structure')
      }
      
      return settings
    })

    // Test 2: Keyword Research
    const researchResult = await this.runTest('Keyword Research', async () => {
      const operationId = 'test-research-' + Date.now()
      trackOperation(operationId, TEST_CONFIG.userId, 'research', 'gemini')
      
      try {
        const result = await conductKeywordResearch(TEST_CONFIG.keywords, TEST_CONFIG.userId, {
          maxSources: 3,
          includeAcademic: false,
          includeNews: true,
          includeBlog: true
        })
        
        completeOperation(operationId, true, result.tokensUsed, result.cost)
        
        if (!result.sources || result.sources.length === 0) {
          throw new Error('No sources found in research')
        }
        
        return result
        
      } catch (error) {
        completeOperation(operationId, false, 0, 0, 'RESEARCH_FAILED')
        throw error
      }
    })

    if (!researchResult.success) {
      console.log('⚠️  Skipping dependent tests due to research failure')
      this.printSummary()
      return
    }

    // Test 3: Outline Generation
    const outlineResult = await this.runTest('Outline Generation', async () => {
      const operationId = 'test-outline-' + Date.now()
      trackOperation(operationId, TEST_CONFIG.userId, 'outline', 'gemini')
      
      try {
        const outline = await generateBlogOutline(
          TEST_CONFIG.keywords,
          researchResult.data,
          TEST_CONFIG.userId,
          {
            targetWordCount: 1500,
            targetAudience: 'intermediate developers',
            contentType: 'guide',
            maxChapters: 4
          }
        )
        
        completeOperation(operationId, true, 500, 0.01) // Estimated
        
        if (!outline.title || !outline.chapters || outline.chapters.length === 0) {
          throw new Error('Invalid outline structure')
        }
        
        return outline
        
      } catch (error) {
        completeOperation(operationId, false, 0, 0, 'OUTLINE_FAILED')
        throw error
      }
    })

    if (!outlineResult.success) {
      console.log('⚠️  Skipping content generation due to outline failure')
      this.printSummary()
      return
    }

    // Test 4: Content Generation (simplified)
    const contentResult = await this.runTest('Content Generation', async () => {
      const operationId = 'test-content-' + Date.now()
      trackOperation(operationId, TEST_CONFIG.userId, 'content', 'gemini')
      
      try {
        const content = await generateBlogContent(
          outlineResult.data,
          researchResult.data,
          TEST_CONFIG.userId,
          {
            includeIntroduction: true,
            includeConclusion: true,
            enableInternalLinking: false, // Disable for testing
            enableExternalCitations: false, // Disable for testing
            maxLinksPerSection: 0,
            writingStyle: 'professional',
            targetAudience: 'intermediate developers'
          }
        )
        
        completeOperation(operationId, true, content.tokensUsed, content.cost)
        
        if (!content.content || content.content.length < 500) {
          throw new Error('Generated content too short')
        }
        
        return content
        
      } catch (error) {
        completeOperation(operationId, false, 0, 0, 'CONTENT_FAILED')
        throw error
      }
    })

    // Test 5: Content Quality Validation
    if (contentResult.success) {
      await this.runTest('Content Quality Validation', async () => {
        const validation = await validateContentQuality(
          contentResult.data.content,
          contentResult.data.title,
          {
            excerpt: contentResult.data.excerpt,
            tags: contentResult.data.metadata.tags,
            categories: contentResult.data.metadata.categories
          },
          await getOrCreateAISettings(TEST_CONFIG.userId),
          TEST_CONFIG.userId
        )
        
        if (typeof validation.score.overall !== 'number') {
          throw new Error('Invalid quality score')
        }
        
        return validation
      })
    }

    // Test 6: Performance Monitoring
    await this.runTest('Performance Monitoring', async () => {
      const stats = getUserPerformanceStats(TEST_CONFIG.userId)
      
      if (typeof stats.totalOperations !== 'number') {
        throw new Error('Invalid performance stats')
      }
      
      return stats
    })

    // Test 7: Error Handling
    await this.runTest('Error Handling', async () => {
      try {
        // Test with invalid keywords
        await conductKeywordResearch([], TEST_CONFIG.userId)
        throw new Error('Should have thrown error for empty keywords')
      } catch (error) {
        // This should fail, which is expected
        if (error instanceof Error && error.message.includes('empty')) {
          return { errorHandled: true }
        }
        throw error
      }
    })

    this.printSummary()
  }

  private printSummary(): void {
    console.log('\n' + '=' .repeat(50))
    console.log('📊 TEST SUMMARY')
    console.log('=' .repeat(50))
    
    const passed = this.results.filter(r => r.success).length
    const failed = this.results.filter(r => !r.success).length
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0)
    
    console.log(`✅ Passed: ${passed}`)
    console.log(`❌ Failed: ${failed}`)
    console.log(`⏱️  Total Duration: ${totalDuration}ms`)
    console.log(`📈 Success Rate: ${((passed / this.results.length) * 100).toFixed(1)}%`)
    
    if (failed > 0) {
      console.log('\n🔍 FAILED TESTS:')
      this.results
        .filter(r => !r.success)
        .forEach(r => {
          console.log(`  • ${r.name}: ${r.error}`)
        })
    }
    
    console.log('\n📋 DETAILED RESULTS:')
    this.results.forEach(r => {
      const status = r.success ? '✅' : '❌'
      console.log(`  ${status} ${r.name} (${r.duration}ms)`)
    })
    
    // Performance insights
    const avgDuration = totalDuration / this.results.length
    console.log(`\n⚡ Average test duration: ${avgDuration.toFixed(0)}ms`)
    
    if (avgDuration > 10000) {
      console.log('⚠️  Warning: Tests are running slowly. Consider optimizing AI operations.')
    }
    
    console.log('\n🎉 Testing completed!')
  }

  getResults(): TestResult[] {
    return this.results
  }
}

// Export test runner
export const runAISystemTests = async (): Promise<TestResult[]> => {
  const tester = new AISystemTester()
  await tester.runAllTests()
  return tester.getResults()
}

// CLI execution
if (require.main === module) {
  runAISystemTests()
    .then(() => {
      console.log('All tests completed!')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Test runner failed:', error)
      process.exit(1)
    })
}
